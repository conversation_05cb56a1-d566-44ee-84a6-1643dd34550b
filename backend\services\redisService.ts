import Redis from 'ioredis';

export class RedisService {
  private static instance: RedisService;
  private redis: Redis;

  private constructor() {
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      password: process.env.REDIS_PASSWORD || undefined,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });

    this.redis.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    this.redis.on('connect', () => {
      console.log('Redis connected successfully');
    });
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  public getClient(): Redis {
    return this.redis;
  }

  // 设置键值对，带过期时间（秒）
  public async set(key: string, value: string, expireInSeconds?: number): Promise<void> {
    if (expireInSeconds) {
      await this.redis.setex(key, expireInSeconds, value);
    } else {
      await this.redis.set(key, value);
    }
  }

  // 获取值
  public async get(key: string): Promise<string | null> {
    return await this.redis.get(key);
  }

  // 删除键
  public async del(key: string): Promise<number> {
    return await this.redis.del(key);
  }

  // 检查键是否存在
  public async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  // 设置键的过期时间
  public async expire(key: string, seconds: number): Promise<boolean> {
    const result = await this.redis.expire(key, seconds);
    return result === 1;
  }

  /**
   * [新增] 从Redis中获取一个缓存的JSON对象。
   * 这个方法会自动处理从字符串到JSON对象的转换。
   * @param key 缓存键
   * @returns 解析后的对象，如果不存在或解析失败则返回 null
   */
  public async getCachedObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.get(key);
      if (!jsonString) {
        console.log(`[RedisService] Cache MISS for key: ${key}`);
        return null;
      }
      console.log(`[RedisService] Cache HIT for key: ${key}`);
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`[RedisService] Failed to get or parse cached object for key: ${key}`, error);
      return null;
    }
  }

  /**
   * [新增] 将一个JSON对象存入Redis缓存，并设置过期时间。
   * @param key 缓存键
   * @param object 要缓存的对象
   * @param ttlSeconds 过期时间（秒）。默认设置为1小时。
   */
  public async setCachedObject(key: string, object: any, ttlSeconds: number = 3600): Promise<void> {
    try {
      const jsonString = JSON.stringify(object);
      await this.set(key, jsonString, ttlSeconds);
      console.log(`[RedisService] Object cached successfully for key: ${key} with TTL: ${ttlSeconds}s`);
    } catch (error) {
      console.error(`[RedisService] Failed to set cached object for key: ${key}`, error);
    }
  }

  // 获取键的剩余过期时间
  public async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }

  // 原子性递增
  public async incr(key: string): Promise<number> {
    return await this.redis.incr(key);
  }

  // 关闭连接
  public async disconnect(): Promise<void> {
    await this.redis.disconnect();
  }
}

export default RedisService;
