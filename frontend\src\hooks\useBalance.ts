import { useEffect, useCallback, useRef } from 'react';
import { useBalanceStore } from '../stores/balanceStore';
import { getUserBalance, checkBalanceUpdate, checkUserBalance } from '../lib/api/balance';
import { useToastContext } from '../contexts/ToastContext';
import useAuthStore from '../stores/authStore';

export const useBalance = () => {
  const { showError } = useToastContext();
  const { isAuthenticated } = useAuthStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  const {
    balance,
    isLoading,
    setBalance,
    updateBalance,
    clearBalance,
    setLoading,
    shouldRefreshBalance,
    isBalanceStale
  } = useBalanceStore();

  // 获取最新余额
  const fetchBalance = useCallback(async (force = false) => {
    if (!isAuthenticated) {
      clearBalance();
      return;
    }

    if (!force && !shouldRefreshBalance()) {
      return;
    }

    try {
      setLoading(true);
      const newBalance = await getUserBalance();
      setBalance(newBalance);
    } catch (error) {
      console.error('获取余额失败:', error);
      if (force) {
        showError('获取余额失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, shouldRefreshBalance, setBalance, setLoading, clearBalance, showError]);

  // 轻量级余额检查
  const checkBalance = useCallback(async () => {
    if (!isAuthenticated || !balance) return;

    try {
      const { balance: newBalance, hasChanged } = await checkBalanceUpdate();
      if (hasChanged) {
        setBalance(newBalance);
      }
    } catch (error) {
      console.error('检查余额更新失败:', error);
      // 静默失败，不显示错误提示
    }
  }, [isAuthenticated, balance, setBalance]);

  // 余额预检查（用于"立即体验"按钮）- API版本
  const preCheckBalance = useCallback(async (type: 'mock' | 'formal'): Promise<boolean> => {
    if (!isAuthenticated) return false;

    try {
      const balanceData = await checkUserBalance();
      return type === 'mock' ? balanceData.hasMockCredits : balanceData.hasFormalCredits;
    } catch (error) {
      console.error('Pre-check balance failed:', error);
      showError('余额检查失败，请稍后重试');
      return false;
    }
  }, [isAuthenticated, showError]);

  // 纯缓存余额检查（用于"立即体验"按钮，仅用于日志记录，不阻拦用户）
  const checkCachedBalanceForLogging = useCallback((type: 'mock' | 'formal'): void => {
    if (!isAuthenticated) {
      console.log('🔍 CachedBalance: User not authenticated');
      return;
    }

    if (!balance) {
      console.log('🔍 CachedBalance: No cached balance found');
      return;
    }

    const credits = type === 'mock' ? balance.mockInterviewCredits : balance.formalInterviewCredits;
    const hasCredits = credits > 0;

    console.log(`🔍 CachedBalance: ${type} interview check:`, {
      type,
      credits,
      hasCredits,
      action: 'logging_only_no_blocking'
    });
  }, [isAuthenticated, balance]);

  // 强制刷新余额（用于支付、兑换等操作后）
  const refreshBalance = useCallback(() => {
    return fetchBalance(true);
  }, [fetchBalance]);

  // 检查是否有足够的余额
  const hasEnoughCredits = useCallback((type: 'mock' | 'formal'): boolean => {
    if (!balance) return false;

    const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
    return balance[fieldName] > 0;
  }, [balance]);

  // 获取指定类型的余额
  const getCredits = useCallback((type: 'mock' | 'formal'): number => {
    if (!balance) return 0;
    
    const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
    return balance[fieldName];
  }, [balance]);

  // 扣费后更新本地缓存
  const deductCredits = useCallback((type: 'mock' | 'formal', amount = 1) => {
    if (!balance) return;
    
    const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
    updateBalance({
      [fieldName]: Math.max(0, balance[fieldName] - amount)
    });
  }, [balance, updateBalance]);

  // 页面焦点恢复时检查余额（开发环境下禁用，减少频繁请求）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      return; // 开发环境下禁用焦点检查
    }

    const handleFocus = () => {
      if (isBalanceStale()) {
        fetchBalance();
      } else {
        checkBalance();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [fetchBalance, checkBalance, isBalanceStale]);

  // 定期检查余额更新
  useEffect(() => {
    if (!isAuthenticated) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // 开发环境：每30分钟检查一次；生产环境：每10分钟检查一次
    const checkInterval = process.env.NODE_ENV === 'development' ? 30 * 60 * 1000 : 10 * 60 * 1000;
    intervalRef.current = setInterval(() => {
      checkBalance();
    }, checkInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isAuthenticated, checkBalance]);

  // 初始化时获取余额
  useEffect(() => {
    if (isAuthenticated && shouldRefreshBalance()) {
      fetchBalance();
    }
  }, [isAuthenticated, fetchBalance, shouldRefreshBalance]);

  // 用户登出时清理余额
  useEffect(() => {
    if (!isAuthenticated) {
      clearBalance();
    }
  }, [isAuthenticated, clearBalance]);

  return {
    balance,
    isLoading,
    fetchBalance,
    refreshBalance,
    hasEnoughCredits,
    getCredits,
    deductCredits,
    checkBalance,
    preCheckBalance,
    checkCachedBalanceForLogging
  };
};
