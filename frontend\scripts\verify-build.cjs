#!/usr/bin/env node

/**
 * 终极构建验证脚本
 * 使用更精确的方法检查生产构建是否包含HMR相关代码
 */

const fs = require('fs');
const path = require('path');

const distPath = path.join(__dirname, '..', 'dist');
const criticalPatterns = [
  'localhost:5173',
  '/@vite/client',
  '[vite] connecting',
  'import.meta.hot',
];
let found = false;

function searchInDir(dir) {
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.lstatSync(fullPath);
    if (stat.isDirectory()) {
      searchInDir(fullPath);
    } else if (file.endsWith('.js')) {
      const content = fs.readFileSync(fullPath, 'utf-8');
      for (const pattern of criticalPatterns) {
        if (content.includes(pattern)) {
          console.error(`🚨 CRITICAL HMR CODE FOUND in ${fullPath}: Pattern "${pattern}"`);
          found = true;
        }
      }
    }
  }
}

console.log('🔍 Verifying build output for HMR code...');
searchInDir(distPath);

if (found) {
  console.error('\n❌ Verification failed. Critical HMR code detected in build output.');
  process.exit(1);
} else {
  console.log('\n✅ Verification passed. No critical HMR code found.');
}
