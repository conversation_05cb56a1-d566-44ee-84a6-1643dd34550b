import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { getUsageRecords, UsageRecord, UsageType } from '../lib/api/usageRecords';
import useDocumentTitle from '../hooks/useDocumentTitle';



const UsageRecordsPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('消耗记录');

  const [records, setRecords] = useState<UsageRecord[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [typeFilter, setTypeFilter] = useState<UsageType | 'all'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const itemsPerPage = 10;

  // 加载消耗记录数据
  useEffect(() => {
    const loadRecords = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await getUsageRecords({
          page: currentPage,
          pageSize: itemsPerPage,
          type: typeFilter
        });

        // 调试：打印后端返回的数据
        console.log('消耗记录API返回数据:', response);

        setRecords(response.records);
        setTotalCount(response.total || 0);
      } catch (err: any) {
        setError(err.message || '获取消耗记录失败');
        console.error('获取消耗记录失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecords();
  }, [currentPage, typeFilter]);

  // 获取类型显示文本和样式
  const getTypeDisplay = (type: UsageType) => {
    switch (type) {
      case 'mock_interview':
        return { text: '模拟面试', className: 'bg-blue-100 text-blue-800' };
      case 'formal_interview':
        return { text: '正式面试', className: 'bg-purple-100 text-purple-800' };
      case 'credit_recharge':
        return { text: '面巾充值', className: 'bg-green-100 text-green-800' };
      default:
        return { text: '未知', className: 'bg-gray-100 text-gray-800' };
    }
  };

  // 获取变动数量显示样式
  const getAmountDisplay = (amount: number, type: UsageType) => {
    const isPositive = amount > 0;
    const displayAmount = isPositive ? `+${amount}` : amount.toString();
    
    // 根据类型添加单位
    let unit = '';
    if (type === 'credit_recharge') {
      unit = '点';
    } else {
      unit = '次';
    }

    return {
      text: `${displayAmount}${unit}`,
      className: isPositive ? 'text-green-600' : 'text-red-600'
    };
  };

  // 使用服务器端分页，不需要前端过滤和分页
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;

  return (
    <div className="container mx-auto px-6 py-6">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">消耗记录</h1>
        <p className="text-gray-600 mt-2">查看您的面试次数和面巾使用记录</p>
      </header>

      {/* 类型筛选 */}
      <div className="mb-6">
        <div className="flex gap-2">
          <button
            onClick={() => setTypeFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              typeFilter === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            全部
          </button>
          <button
            onClick={() => setTypeFilter('mock_interview')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              typeFilter === 'mock_interview'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            模拟面试
          </button>
          <button
            onClick={() => setTypeFilter('formal_interview')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              typeFilter === 'formal_interview'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            正式面试
          </button>
          <button
            onClick={() => setTypeFilter('credit_recharge')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              typeFilter === 'credit_recharge'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            面巾充值
          </button>
        </div>
      </div>

      {/* 消耗记录表格 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">日期</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">消费类型</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">变动数量</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">原因</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {records.map((record) => {
                    const typeDisplay = getTypeDisplay(record.type);
                    const amountDisplay = getAmountDisplay(record.amount, record.type);
                    return (
                      <tr key={record.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 text-sm text-gray-600">{record.date}</td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeDisplay.className}`}>
                            {typeDisplay.text}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`text-sm font-medium ${amountDisplay.className}`}>
                            {amountDisplay.text}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">{record.reason}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-100 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  显示 {startIndex + 1} 到 {Math.min(startIndex + itemsPerPage, totalCount)} 条，共 {totalCount} 条记录
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>
                  <span className="px-3 py-1 text-sm text-gray-600">
                    {currentPage} / {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* 空状态 */}
      {!isLoading && records.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">暂无消耗记录</div>
          <div className="text-gray-500 text-sm">您还没有任何消耗记录</div>
        </div>
      )}
    </div>
  );
};

export default UsageRecordsPage;
