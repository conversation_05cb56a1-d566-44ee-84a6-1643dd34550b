import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import { UsageType } from './usage-records';

const router = express.Router();

interface ExchangeRequestBody {
  type: 'mock' | 'formal';
}

/**
 * 面巾兑换面试次数
 * POST /api/exchange
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { type } = req.body as ExchangeRequestBody;

    console.log('🔍 Exchange request received:', { userId, body: req.body, type });

    if (!type || !['mock', 'formal'].includes(type)) {
      console.log('❌ Invalid exchange type:', { type, body: req.body });
      return res.status(400).json({
        success: false,
        message: '兑换类型无效'
      });
    }

    // 定义兑换规则
    const exchangeRules = {
      mock: { cost: 100, creditField: 'mockInterviewCredits', description: 'AI模拟面试' },
      formal: { cost: 200, creditField: 'formalInterviewCredits', description: 'AI正式面试' }
    };

    const rule = exchangeRules[type];

    // 使用事务确保原子性
    const result = await prisma.$transaction(async (tx) => {
      // 获取用户当前余额
      let userBalance = await tx.userBalance.findUnique({
        where: { userId }
      });

      // 如果用户没有余额记录，创建默认记录
      if (!userBalance) {
        userBalance = await tx.userBalance.create({
          data: {
            userId,
            mockInterviewCredits: 0,
            formalInterviewCredits: 0,
            mianshijunBalance: 0
          }
        });
      }

      // 检查面巾余额是否足够
      if (userBalance.mianshijunBalance < rule.cost) {
        throw new Error(`面巾余额不足，需要${rule.cost}面巾，当前余额：${userBalance.mianshijunBalance}面巾`);
      }

      // 执行兑换：扣除面巾，增加面试次数
      const updateData: any = {
        mianshijunBalance: {
          decrement: rule.cost
        }
      };
      updateData[rule.creditField] = {
        increment: 1
      };

      const updatedBalance = await tx.userBalance.update({
        where: { userId },
        data: updateData
      });

      // 创建消耗记录（面巾消耗 - 负值，面试次数获得 - 正值）
      // 1. 面巾消耗记录
      await tx.usageRecord.create({
        data: {
          userId: userId,
          type: UsageType.CREDIT_RECHARGE,
          amount: -rule.cost, // 负数表示消耗面巾
          reason: `面巾兑换${rule.description} - 消耗${rule.cost}面巾`
        }
      });

      // 2. 面试次数获得记录
      const interviewUsageType = type === 'mock' ? UsageType.MOCK_INTERVIEW : UsageType.FORMAL_INTERVIEW;
      await tx.usageRecord.create({
        data: {
          userId: userId,
          type: interviewUsageType,
          amount: 1, // 正数表示获得面试次数
          reason: `面巾兑换${rule.description} - 获得1次机会`
        }
      });

      return updatedBalance;
    }, {
      timeout: 5000, // 5秒超时
      isolationLevel: 'ReadCommitted'
    });

    return res.status(200).json({
      success: true,
      message: `兑换成功！获得1次${rule.description}机会`,
      data: {
        exchangeType: type,
        cost: rule.cost,
        description: rule.description,
        newBalance: {
          mianshijunBalance: result.mianshijunBalance,
          mockInterviewCredits: result.mockInterviewCredits,
          formalInterviewCredits: result.formalInterviewCredits
        }
      }
    });

  } catch (error: any) {
    console.error('面巾兑换失败:', error);
    
    return res.status(400).json({
      success: false,
      message: error.message || '兑换失败，请稍后再试'
    });
  }
});

export default router;
