# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build artifacts
dist/
build/
out/
coverage/
*.local
dist-ssr/

# IDE & Editor specific
.idea/
.vscode/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Operating System files
.DS_Store
Thumbs.db
ehthumbs.db

# Environment variables
.env
.env*.local
!.env.example

# Log files
logs/
*.log
backend-*.txt
frontend-*.txt

# PM2 logs and process files
ecosystem.config.js
.pm2/
pm2.json

# Backup files
*.backup
*.bak
*.tmp
*.temp

# Frontend specific
frontend/dist/
frontend/node_modules/
frontend/.DS_Store
frontend/.env*
frontend/coverage/
frontend/.bolt/

# Backend specific
backend/dist/
backend/node_modules/
backend/.DS_Store
backend/.env*
backend/coverage/
backend/logs/
backend/package-lock.json

# Admin Frontend specific
admin-frontend/dist/
admin-frontend/node_modules/
admin-frontend/.DS_Store
admin-frontend/.env*
admin-frontend/coverage/

# Packages specific
packages/*/dist/
packages/*/node_modules/
packages/*/.DS_Store
packages/*/.env*
packages/*/coverage/

# Documentation and development plan logs (specific to this project)
docs/
docs/development-plan/log/
docs/development-plan/*/log/

# Database
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.production

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/
