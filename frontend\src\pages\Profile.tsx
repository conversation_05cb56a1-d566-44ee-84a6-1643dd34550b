import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Phone, Mail, Calendar, Edit3, Settings, Copy, Check } from 'lucide-react';
import useAuthStore from '../stores/authStore';
import useUserStore from '../stores/userStore';
import useDocumentTitle from '../hooks/useDocumentTitle';
import { useToastContext } from '../contexts/ToastContext';

const Profile: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('个人中心');

  const navigate = useNavigate();
  const { user, token } = useAuthStore();
  const { userInfo, isLoadingUserInfo, userInfoError, setUserInfo, setLoadingUserInfo, setUserInfoError } = useUserStore();
  const { showSuccess } = useToastContext();

  const [isEditingNickname, setIsEditingNickname] = useState(false);
  const [newNickname, setNewNickname] = useState('');
  const [isUpdatingNickname, setIsUpdatingNickname] = useState(false);
  const [copied, setCopied] = useState(false);

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token) return;

    setLoadingUserInfo(true);
    setUserInfoError(null);

    try {
      // 使用统一的API基础URL
      const API_BASE_URL = process.env.NODE_ENV === 'production'
        ? 'https://mianshijun.xyz'
        : '';

      const response = await fetch(`${API_BASE_URL}/api/users/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const userData = await response.json();
      // 后端返回格式：{success: true, data: {...}}
      setUserInfo(userData.data || userData);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setUserInfoError('获取用户信息失败，请稍后重试');
    } finally {
      setLoadingUserInfo(false);
    }
  };

  // 修改昵称
  const updateNickname = async () => {
    if (!token || !newNickname.trim()) return;

    setIsUpdatingNickname(true);

    try {
      // 使用统一的API基础URL
      const API_BASE_URL = process.env.NODE_ENV === 'production'
        ? 'https://mianshijun.xyz'
        : '';

      const response = await fetch(`${API_BASE_URL}/api/users/me`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'nickname',
          nickname: newNickname.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '修改昵称失败');
      }

      const updatedUser = await response.json();
      setUserInfo(updatedUser);
      setIsEditingNickname(false);
      setNewNickname('');
    } catch (error) {
      console.error('修改昵称失败:', error);
      alert(error instanceof Error ? error.message : '修改昵称失败，请稍后重试');
    } finally {
      setIsUpdatingNickname(false);
    }
  };

  // 开始编辑昵称
  const startEditingNickname = () => {
    setNewNickname(userInfo?.name || '');
    setIsEditingNickname(true);
  };

  // 取消编辑昵称
  const cancelEditingNickname = () => {
    setIsEditingNickname(false);
    setNewNickname('');
  };

  // 复制UID到剪贴板
  const handleCopyUID = async () => {
    if (!userInfo?.id) return;

    try {
      await navigator.clipboard.writeText(userInfo.id);
      setCopied(true);
      showSuccess('UID复制成功');
      setTimeout(() => setCopied(false), 2000); // 2秒后重置状态
    } catch (err) {
      console.error('Failed to copy UID:', err);
      showSuccess('复制失败，请手动复制');
    }
  };

  // 截断UID显示
  const truncateUID = (uid: string, maxLength: number = 8) => {
    if (uid.length <= maxLength) return uid;
    return uid.substring(0, maxLength) + '...';
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 页面加载时获取用户信息
  useEffect(() => {
    fetchUserInfo();
  }, [token]);

  if (isLoadingUserInfo) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载用户信息中...</p>
        </div>
      </div>
    );
  }

  if (userInfoError) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <p className="text-red-600 mb-4">{userInfoError}</p>
          <button
            onClick={fetchUserInfo}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
        <button
          onClick={() => navigate('/settings')}
          className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <Settings className="w-4 h-4" />
          设置
        </button>
      </div>

      {/* 用户头像和基本信息 */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center gap-6">
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-2xl font-bold">
            {userInfo?.name?.[0]?.toUpperCase() || userInfo?.email?.[0]?.toUpperCase() || 'U'}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {isEditingNickname ? (
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={newNickname}
                    onChange={(e) => setNewNickname(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入昵称"
                    maxLength={50}
                  />
                  <button
                    onClick={updateNickname}
                    disabled={isUpdatingNickname || !newNickname.trim()}
                    className="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isUpdatingNickname ? '保存中...' : '保存'}
                  </button>
                  <button
                    onClick={cancelEditingNickname}
                    className="px-3 py-1 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    取消
                  </button>
                </div>
              ) : (
                <>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {userInfo?.name || '未设置昵称'}
                  </h2>
                  <button
                    onClick={startEditingNickname}
                    className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                    title="编辑昵称"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                </>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">UID: </span>
              <div className="flex items-center gap-2">
                <span
                  className="text-gray-600 cursor-pointer"
                  title={userInfo?.id}
                >
                  {userInfo?.id ? truncateUID(userInfo.id) : ''}
                </span>
                <button
                  onClick={handleCopyUID}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="复制UID"
                >
                  {copied ? <Check className="w-3 h-3 text-green-500" /> : <Copy className="w-3 h-3" />}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 详细信息卡片 */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* 基本信息 */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <User className="w-5 h-5 text-blue-600" />
            基本信息
          </h3>
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">邮箱地址</p>
                <p className="text-gray-900">{userInfo?.email}</p>
                <p className="text-xs text-gray-400">（暂不允许修改邮箱地址）</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-sm text-gray-500">注册时间</p>
                <p className="text-gray-900">
                  {userInfo?.createdAt ? formatDate(userInfo.createdAt) : '未知'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 联系信息 */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Phone className="w-5 h-5 text-blue-600" />
            联系信息
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">手机号码</p>
                  <p className="text-gray-900">
                    {userInfo?.phoneNumber || '未绑定'}
                  </p>
                </div>
              </div>
              <button
                onClick={() => navigate('/settings')}
                className="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
              >
                {userInfo?.phoneNumber ? '修改' : '绑定'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
