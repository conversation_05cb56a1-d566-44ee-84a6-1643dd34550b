import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../stores/authStore';
import useInterviewStore from '../stores/interviewStore';
import { useMockInterviewStore, useMockConfig, useMockSession, useMockMessages, useMockSessionActions } from '../stores/mockInterviewStore';
import { targetPositionService, TargetPosition } from '../lib/api/apiService';
import { usePositions, usePositionActions } from '../stores/positionStore';
import { attachClientLoggerWebSocket } from '../utils/clientLogger';
import { AudioBufferManager, DEFAULT_BUFFER_CONFIG } from '../utils/audioBufferManager';
import { VADProcessor, VADResult } from '../utils/vadProcessor';
import CryptoJS from 'crypto-js';
import { useWebSocket as useUnifiedWebSocket } from '../providers/WebSocketProvider';
import type { InterviewConfig } from '../managers/UnifiedWebSocketManager';
import { unstable_batchedUpdates } from 'react-dom';
import { useMessages, useSessionInfo, useMessageActions, usePerformanceStats } from '../stores/messageStore';


// 🔥 简化的WebSocket hook - 只负责连接和消息传递
const useWebSocket = (url: string | null) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [lastMessage, setLastMessage] = useState<MessageEvent | null>(null);
  const [readyState, setReadyState] = useState(3); // CLOSED

  useEffect(() => {
    if (!url) return;

    const ws = new WebSocket(url);

    ws.onopen = () => {
      setReadyState(1); // OPEN
      console.log('🔗 WebSocket connected');
      attachClientLoggerWebSocket(ws);
    };

    ws.onclose = () => {
      setReadyState(3); // CLOSED
      console.log('🔗 WebSocket disconnected');
    };

    ws.onmessage = (event) => {
      console.log('🔍 WebSocket message received:', {
        dataType: typeof event.data,
        dataLength: event.data.length,
        dataPreview: event.data.substring(0, 200) + (event.data.length > 200 ? '...' : ''),
        timestamp: new Date().toISOString()
      });
      setLastMessage(event);
    };

    ws.onerror = (error) => {
      console.error('❌ WebSocket error:', error);
    };

    setSocket(ws);

    return () => {
      ws.close();
    };
  }, [url]);

  const sendMessage = useCallback((data: ArrayBuffer) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(data);
    }
  }, [socket]);

  return {
    sendMessage,
    lastMessage,
    readyState
  };
};

// 🔥 简化的类型定义
export interface Message {
  id: string;
  content: string;
  type: 'interviewer' | 'ai-suggestion';
  timestamp: number;
}

// 🔥 原来的批处理Hook已被Zustand store替代，提供更好的性能和状态管理

export interface InterviewSession {
  id: string;
  companyName: string;
  positionName: string;
  startTime: number;
  elapsedTime: number;
  messages: Message[];
  isActive: boolean;
}

// 🔥 Phase 2: 转录状态枚举
enum TranscriptionState {
  INTERMEDIATE = 'intermediate',  // 中间结果，实时更新
  FINALIZING = 'finalizing',     // 正在最终化
  FINAL = 'final',               // 最终结果，不可更改
  LLM_PROCESSING = 'llm_processing', // LLM处理中
  COMPLETED = 'completed'        // 完全完成
}

// 🔥 Phase 2: 转录消息接口
interface TranscriptionMessage {
  id: string;
  content: string;
  state: TranscriptionState;
  timestamp: number;
  llmTriggered: boolean;
}

// 🔥 Phase 2: 统一生命周期管理器
class TranscriptionLifecycleManager {
  private currentMessage: TranscriptionMessage | null = null;
  private messageHistory: TranscriptionMessage[] = [];
  private onUpdateCallback: ((messages: TranscriptionMessage[]) => void) | null = null;

  constructor(onUpdate?: (messages: TranscriptionMessage[]) => void) {
    this.onUpdateCallback = onUpdate || null;
  }

  handleIntermediateResult(text: string): TranscriptionMessage {
    if (!this.currentMessage) {
      // 创建新的中间结果消息
      this.currentMessage = this.createNewMessage(text, TranscriptionState.INTERMEDIATE);
      console.log('🔧 Created new INTERMEDIATE message:', {
        id: this.currentMessage.id,
        content: text,
        state: this.currentMessage.state
      });
    } else {
      // 更新现有中间结果
      this.updateCurrentMessage(text);
      console.log('🔧 Updated INTERMEDIATE message:', {
        id: this.currentMessage.id,
        content: text,
        state: this.currentMessage.state
      });
    }

    this.notifyUpdate();
    return this.currentMessage;
  }

  handleFinalResult(text: string): TranscriptionMessage {
    if (this.currentMessage) {
      // 将当前消息标记为最终
      const finalMessage = this.finalizeCurrentMessage(text);
      console.log('🔧 Finalized message:', {
        id: finalMessage.id,
        content: text,
        state: finalMessage.state,
        llmTriggered: finalMessage.llmTriggered
      });

      // 清空当前消息，准备下一句话
      this.currentMessage = null;
      this.notifyUpdate();
      return finalMessage;
    } else {
      // 没有当前消息，直接创建最终消息
      const finalMessage = this.createNewMessage(text, TranscriptionState.FINAL);
      finalMessage.llmTriggered = true;
      console.log('🔧 Created new FINAL message (no current):', {
        id: finalMessage.id,
        content: text,
        state: finalMessage.state
      });

      this.notifyUpdate();
      return finalMessage;
    }
  }

  private createNewMessage(text: string, state: TranscriptionState): TranscriptionMessage {
    const message: TranscriptionMessage = {
      id: `transcription-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: text,
      state: state,
      timestamp: Date.now(),
      llmTriggered: false
    };

    // 添加到消息历史（按时间顺序）
    this.messageHistory.push(message);
    return message;
  }

  private updateCurrentMessage(text: string): void {
    if (this.currentMessage) {
      this.currentMessage.content = text;
      this.currentMessage.timestamp = Date.now();
    }
  }

  private finalizeCurrentMessage(text: string): TranscriptionMessage {
    if (this.currentMessage) {
      this.currentMessage.content = text;
      this.currentMessage.state = TranscriptionState.FINAL;
      this.currentMessage.llmTriggered = true;
      this.currentMessage.timestamp = Date.now();
      return this.currentMessage;
    }
    throw new Error('No current message to finalize');
  }

  private notifyUpdate(): void {
    if (this.onUpdateCallback) {
      this.onUpdateCallback([...this.messageHistory]);
    }
  }

  getCurrentMessage(): TranscriptionMessage | null {
    return this.currentMessage;
  }

  getMessageHistory(): TranscriptionMessage[] {
    return [...this.messageHistory];
  }

  getDebugInfo(): object {
    return {
      currentMessage: this.currentMessage ? {
        id: this.currentMessage.id,
        state: this.currentMessage.state,
        contentLength: this.currentMessage.content.length
      } : null,
      historyCount: this.messageHistory.length,
      totalMessages: this.messageHistory.length + (this.currentMessage ? 1 : 0)
    };
  }
}

// 🔥 Phase 3: 事件驱动架构
enum TranscriptionEvent {
  INTERMEDIATE_RESULT = 'intermediate_result',
  FINAL_RESULT = 'final_result',
  LLM_TRIGGER = 'llm_trigger',
  LLM_RESPONSE = 'llm_response',
  SESSION_COMPLETE = 'session_complete',
  ERROR_OCCURRED = 'error_occurred'
}

class TranscriptionEventBus {
  private listeners: Map<TranscriptionEvent, Function[]> = new Map();

  emit(event: TranscriptionEvent, data: any): void {
    const handlers = this.listeners.get(event) || [];
    console.log(`🎯 EventBus: Emitting ${event} with ${handlers.length} handlers`, {
      event,
      dataType: typeof data,
      handlersCount: handlers.length
    });

    handlers.forEach((handler, index) => {
      try {
        handler(data);
      } catch (error) {
        console.error(`❌ EventBus: Handler ${index} for ${event} failed:`, error);
      }
    });
  }

  on(event: TranscriptionEvent, handler: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);

    console.log(`🔧 EventBus: Registered handler for ${event}`, {
      event,
      totalHandlers: this.listeners.get(event)!.length
    });
  }

  off(event: TranscriptionEvent, handler: Function): void {
    const handlers = this.listeners.get(event) || [];
    const index = handlers.indexOf(handler);
    if (index !== -1) {
      handlers.splice(index, 1);
      console.log(`🔧 EventBus: Unregistered handler for ${event}`);
    }
  }

  getDebugInfo(): object {
    const eventCounts: Record<string, number> = {};
    this.listeners.forEach((handlers, event) => {
      eventCounts[event] = handlers.length;
    });

    return {
      totalEvents: this.listeners.size,
      eventCounts,
      totalHandlers: Array.from(this.listeners.values()).reduce((sum, handlers) => sum + handlers.length, 0)
    };
  }
}

// 🔥 Phase 3: 性能监控和自动调优
class PerformanceMonitor {
  private metrics = {
    totalTranscriptions: 0,
    totalLatency: 0,
    averageLatency: 0,
    accuracyScore: 0,
    errorCount: 0,
    successRate: 0,
    llmTriggerCount: 0,
    llmSuccessCount: 0
  };

  private latencyHistory: number[] = [];
  private readonly MAX_HISTORY_SIZE = 50;

  recordTranscriptionProcessed(latency: number, isSuccess: boolean, isFinal: boolean): void {
    this.metrics.totalTranscriptions++;

    if (isSuccess) {
      this.metrics.totalLatency += latency;
      this.latencyHistory.push(latency);

      if (this.latencyHistory.length > this.MAX_HISTORY_SIZE) {
        this.latencyHistory.shift();
      }

      this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.totalTranscriptions;

      if (isFinal) {
        this.metrics.llmTriggerCount++;
      }
    } else {
      this.metrics.errorCount++;
    }

    this.metrics.successRate = ((this.metrics.totalTranscriptions - this.metrics.errorCount) / this.metrics.totalTranscriptions) * 100;
  }

  recordLLMProcessed(isSuccess: boolean): void {
    if (isSuccess) {
      this.metrics.llmSuccessCount++;
    }
  }

  getPerformanceReport(): object {
    return {
      ...this.metrics,
      recentLatencies: this.latencyHistory.slice(-10),
      llmSuccessRate: this.metrics.llmTriggerCount > 0 ? (this.metrics.llmSuccessCount / this.metrics.llmTriggerCount) * 100 : 0,
      recommendations: this.generateRecommendations()
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.averageLatency > 500) {
      recommendations.push('Consider optimizing audio processing for faster response');
    }

    if (this.metrics.successRate < 95) {
      recommendations.push('High error rate detected, check network stability');
    }

    if (this.latencyHistory.length > 5) {
      const recentAvg = this.latencyHistory.slice(-5).reduce((a, b) => a + b, 0) / 5;
      if (recentAvg > this.metrics.averageLatency * 1.5) {
        recommendations.push('Recent performance degradation detected');
      }
    }

    const llmSuccessRate = this.metrics.llmTriggerCount > 0 ? (this.metrics.llmSuccessCount / this.metrics.llmTriggerCount) * 100 : 100;
    if (llmSuccessRate < 90) {
      recommendations.push('LLM processing issues detected, check API connectivity');
    }

    return recommendations;
  }
}

// 格式化时间函数
const formatElapsedTime = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  const formattedHours = hours > 0 ? `${hours}:` : '';
  const formattedMinutes = minutes % 60 < 10 ? `0${minutes % 60}` : `${minutes % 60}`;
  const formattedSeconds = seconds % 60 < 10 ? `0${seconds % 60}` : `${seconds % 60}`;

  return `${formattedHours}${formattedMinutes}:${formattedSeconds}`;
};

interface UseInterviewSessionOptions {
  showError?: (message: string, duration?: number) => void;
  showSuccess?: (message: string, duration?: number) => void;
  showInfo?: (message: string, duration?: number) => void;
}

interface InterviewSessionConfig {
  wsUrl?: string;
  mode?: 'live' | 'mock'; // 新增：面试模式
  enableVAD?: boolean;
  enableSegmentation?: boolean;
  audioConfig?: Partial<typeof DEFAULT_BUFFER_CONFIG>;
  // 兼容参考代码的toast函数
  showError?: (message: string, duration?: number) => void;
  showSuccess?: (message: string, duration?: number) => void;
  showInfo?: (message: string, duration?: number) => void;
}

interface TranscriptionEvent {
  id: string;
  text: string;
  speaker: 'interviewer' | 'candidate';
  timestamp: number;
  confidence?: number;
  isFinal: boolean;
}

interface AudioStats {
  segmentCount: number;
  processingLatency: number;
  averageQuality: number;
  speechDetectionRate: number;
}

// 🔥 完全重写的简化Hook - 支持模式驱动架构
export function useInterviewSession(config: InterviewSessionConfig) {
  // 使用传入的toast函数或默认的空函数
  const showError = config.showError || (() => {});
  const showSuccess = config.showSuccess || (() => {});
  const showInfo = config.showInfo || (() => {});
  const { token, isAuthenticated } = useAuthStore();
  const navigate = useNavigate();
  const mode = config.mode || 'live'; // 默认为live模式

  // 🎯 使用全局状态管理替代本地状态
  const globalPositions = usePositions();
  const { fetchPositions } = usePositionActions();

  // 🔥 添加本地positions状态（参考代码中的做法）
  const [positions, setPositions] = useState<TargetPosition[]>([]);

  // 🔥 根据模式选择不同的状态管理
  const liveStore = useInterviewStore();
  const mockConfig = useMockConfig();
  const mockSession = useMockSession();
  const mockMessages = useMockMessages();
  const mockSessionActions = useMockSessionActions();

  // 统一的配置和操作接口
  const interviewConfig = mode === 'mock' ? mockConfig : liveStore.config;
  const setSharedStream = mode === 'mock' ? mockSessionActions.setSharedStream : liveStore.setSharedStream;
  const setScreenShareStatus = mode === 'mock' ? mockSessionActions.setScreenShareStatus : liveStore.setScreenShareStatus;

  // 🔥 根据模式选择消息管理
  let messages, sessionInfo, addMessage, updateMessage, setMessages, initializeSession, updateSession, performanceStats;

  if (mode === 'mock') {
    // 使用mock store的数据和操作
    messages = mockMessages;
    sessionInfo = {
      id: mockSession.id,
      companyName: mockSession.companyName,
      positionName: mockSession.positionName,
      startTime: mockSession.startTime,
      elapsedTime: mockSession.elapsedTime,
      isActive: mockSession.isActive
    };
    addMessage = (message: any) => mockSessionActions.addMessage({
      ...message,
      type: message.type as 'interviewer' | 'ai-suggestion'
    });
    updateMessage = mockSessionActions.updateMessage;
    setMessages = mockSessionActions.setMessages;
    initializeSession = mockSessionActions.initializeSession;
    updateSession = (updater: any) => {
      const currentSession = mockSession;
      const updated = updater(currentSession);
      mockSessionActions.initializeSession(updated);
    };
    performanceStats = { totalMessages: messages.length, batchedUpdates: 0, skippedUpdates: 0, lastUpdateTime: Date.now() };
  } else {
    // 使用live store的数据和操作
    const liveMessages = useMessages();
    const liveSessionInfo = useSessionInfo();
    const liveMessageActions = useMessageActions();
    const livePerformanceStats = usePerformanceStats();

    messages = liveMessages;
    sessionInfo = liveSessionInfo;
    addMessage = liveMessageActions.addMessage;
    updateMessage = liveMessageActions.updateMessage;
    setMessages = liveMessageActions.setMessages;
    initializeSession = liveMessageActions.initializeSession;
    updateSession = liveMessageActions.updateSession;
    performanceStats = livePerformanceStats;
  }

  // 🔥 音频源抽象函数 - 根据模式选择不同的音频源
  const acquireAudioStream = useCallback(async (): Promise<MediaStream> => {
    try {
      let stream: MediaStream;

      if (mode === 'mock') {
        // AI模拟面试：获取麦克风音频
        console.log('🎤 Acquiring microphone audio for mock interview...');
        stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          },
          video: false
        });
      } else {
        // AI正式面试：获取屏幕共享音频
        console.log('🖥️ Acquiring display media for live interview...');
        stream = await navigator.mediaDevices.getDisplayMedia({
          video: true, // 必须请求视频以共享屏幕
          audio: true  // 同时请求系统音频
        });
      }

      console.log(`✅ Audio stream acquired for ${mode} mode:`, {
        audioTracks: stream.getAudioTracks().length,
        videoTracks: stream.getVideoTracks().length,
        mode
      });

      return stream;
    } catch (error) {
      const errorContext = mode === 'mock' ? '麦克风' : '屏幕共享';
      console.error(`❌ Failed to acquire ${errorContext} for ${mode} mode:`, error);
      throw error;
    }
  }, [mode]);

  // 🔥 初始化会话（仅在组件挂载时执行一次）
  useEffect(() => {
    initializeSession({
      id: `session-${Date.now()}`,
      companyName: '公司名称',
      positionName: '岗位名称',
      startTime: Date.now(),
      elapsedTime: 0,
      isActive: true
    });
  }, []); // 空依赖数组，只在挂载时执行

  // 🔥 构建兼容的session对象（保持向后兼容）
  const session = useMemo(() => ({
    ...sessionInfo,
    messages
  }), [sessionInfo, messages]);

  // 🔥 兼容的setSession函数，将调用转换为Zustand store操作
  const setSession = useCallback((updater: (prev: InterviewSession) => InterviewSession) => {
    const currentSession = {
      ...sessionInfo,
      messages
    };
    const newSession = updater(currentSession);

    // 更新会话信息（除了messages）
    if (newSession.id !== currentSession.id ||
        newSession.companyName !== currentSession.companyName ||
        newSession.positionName !== currentSession.positionName ||
        newSession.startTime !== currentSession.startTime ||
        newSession.elapsedTime !== currentSession.elapsedTime ||
        newSession.isActive !== currentSession.isActive) {
      updateSession(() => ({
        id: newSession.id,
        companyName: newSession.companyName,
        positionName: newSession.positionName,
        startTime: newSession.startTime,
        elapsedTime: newSession.elapsedTime,
        isActive: newSession.isActive,
        messages: newSession.messages
      }));
    }

    // 更新消息（如果有变化）
    if (newSession.messages !== currentSession.messages) {
      setMessages(newSession.messages);
    }
  }, [sessionInfo, messages, updateSession, setMessages]);

  // 🔥 性能监控：定期输出Zustand store的性能统计
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('📊 Zustand Message Store Performance Stats:', performanceStats);
    }, 10000); // 每10秒输出一次

    return () => clearInterval(interval);
  }, [performanceStats]);

  // 🔥 简化的状态管理
  const [socketUrl, setSocketUrl] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(true); // 🔥 修复：默认为true，表示正在识别状态
  const [formattedTime, setFormattedTime] = useState('00:00');

  // 🔥 简化的音频处理引用
  const audioContextRef = useRef<AudioContext | null>(null);
  const processorRef = useRef<ScriptProcessorNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);

  // 🆕 当前正在显示的"中间转写"气泡ID（useRef避免重复渲染）
  const currentTranscriptionIdRef = useRef<string | null>(null);

  // 🔥 新增：消息去重机制，防止重复处理
  const processedMessageIds = useRef(new Set<string>());
  const lastProcessedTime = useRef<number>(0);

  // 🔥 Phase 2 & 3: 初始化架构组件
  const lifecycleManagerRef = useRef<TranscriptionLifecycleManager | null>(null);
  const eventBusRef = useRef<TranscriptionEventBus | null>(null);
  const performanceMonitorRef = useRef<PerformanceMonitor | null>(null);

  // 🔥 智能气泡管理器
  class SmartBubbleManager {
    private lastTranscriptionTime = 0;
    private currentBubbleId: string | null = null;
    private readonly PAUSE_THRESHOLD = 2000; // 2秒

    processTranscription(text: string, timestamp: number, generateId: () => string) {
      const timeSinceLastTranscription = timestamp - this.lastTranscriptionTime;

      if (timeSinceLastTranscription > this.PAUSE_THRESHOLD || !this.currentBubbleId) {
        // 创建新气泡
        this.currentBubbleId = generateId();
        this.lastTranscriptionTime = timestamp;
        return { action: 'create', bubbleId: this.currentBubbleId, text };
      } else {
        // 更新现有气泡
        this.lastTranscriptionTime = timestamp;
        return { action: 'update', bubbleId: this.currentBubbleId, text };
      }
    }

    reset() {
      this.currentBubbleId = null;
      this.lastTranscriptionTime = 0;
    }
  }

  const smartBubbleManagerRef = useRef<SmartBubbleManager | null>(null);

  // 初始化架构组件
  useEffect(() => {
    if (!lifecycleManagerRef.current) {
      lifecycleManagerRef.current = new TranscriptionLifecycleManager();
      console.log('🔧 Initialized TranscriptionLifecycleManager');
    }

    if (!eventBusRef.current) {
      eventBusRef.current = new TranscriptionEventBus();
      console.log('🔧 Initialized TranscriptionEventBus');
    }

    if (!performanceMonitorRef.current) {
      performanceMonitorRef.current = new PerformanceMonitor();
      console.log('🔧 Initialized PerformanceMonitor');
    }

    if (!smartBubbleManagerRef.current) {
      smartBubbleManagerRef.current = new SmartBubbleManager();
      console.log('🔧 Initialized SmartBubbleManager');
    }

    // 🔥 修复：移除EventBus重复注册，简化事件处理
    console.log('🔧 Architecture components initialized (EventBus removed to fix duplication)');

    return () => {
      console.log('🔧 Cleaning up architecture components');
    };
  }, []);

  // 🔥 获取岗位信息（参考代码中的做法）
  useEffect(() => {
    const fetchPositionsData = async () => {
      if (token) {
        try {
          const positionData = await targetPositionService.getTargetPositions();
          setPositions(positionData);
          console.log('✅ Positions fetched successfully:', positionData);
        } catch (error) {
          console.error('❌ Failed to fetch positions:', error);
          showError?.('获取岗位信息失败');
        }
      }
    };

    fetchPositionsData();
  }, [token, showError]);

  // 当岗位信息或selectedPositionId变化时更新session
  useEffect(() => {
    if (interviewConfig.selectedPositionId && positions.length > 0) {
      const selectedPosition = positions.find(p => p.id === interviewConfig.selectedPositionId);

      if (selectedPosition) {
        console.log('✅ Updating session with position info:', {
          positionId: selectedPosition.id,
          companyName: selectedPosition.companyName,
          positionName: selectedPosition.positionName
        });

        updateSession((prev) => ({
          ...prev,
          companyName: selectedPosition.companyName || '公司名称',
          positionName: selectedPosition.positionName || '岗位名称'
        }));
      }
    }
  }, [interviewConfig.selectedPositionId, positions, updateSession]);

  // 构建WebSocket URL
  useEffect(() => {
    if (isAuthenticated && token && interviewConfig.selectedPositionId) {
      const sessionId = `session_${interviewConfig.selectedPositionId}_${Date.now()}`;

      // 使用更健壮的URL构建逻辑
      const getWsBaseUrl = (): string => {
        // 生产环境的判断：优先检查 hostname 是否为生产域名
        if (window.location.hostname === 'mianshijun.xyz' || import.meta.env.PROD) {
          const protocol = 'wss:';
          const host = window.location.host; // 这将是 'mianshijun.xyz'
          return `${protocol}//${host}`;
        }

        // 默认返回开发环境地址
        return 'ws://localhost:3000';
      };

      const baseUrl = getWsBaseUrl();
      const wsUrl = `${baseUrl}/api/ws/interview/${sessionId}?token=${encodeURIComponent(token)}&mode=${mode}&positionId=${encodeURIComponent(interviewConfig.selectedPositionId)}`;
      console.log(`🔗 [useInterviewSession] WebSocket URL: ${wsUrl} (mode: ${mode}, positionId: ${interviewConfig.selectedPositionId})`);
      setSocketUrl(wsUrl);

      // 更新session ID
      updateSession(prev => ({ ...prev, id: sessionId }));
    }
  }, [isAuthenticated, token, interviewConfig.selectedPositionId]);

  // 生成唯一ID的辅助函数
  const generateUniqueId = useCallback((prefix: string): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 🔥 简化的WebSocket连接
  const { sendMessage: sendWebSocketMessage, lastMessage, readyState } = useWebSocket(socketUrl);

  // 🔥 修改后的消息处理 - 实现单一蓝色气泡策略
  useEffect(() => {
    if (lastMessage !== null) {
      console.log('🔍 Processing WebSocket message:', {
        messageType: typeof lastMessage.data,
        messageLength: lastMessage.data.length,
        timestamp: new Date().toISOString()
      });

      try {
        const data = JSON.parse(lastMessage.data);

        // 🔥 修复字段映射：处理不同的字段格式
        if (data.type === 'transcription') {
          // 优先使用 isFinal 字段（InterviewSessionManager发送的格式）
          if (typeof data.isFinal !== 'undefined') {
            // 已经有 isFinal 字段，确保 isPartial 字段一致
            data.isPartial = !data.isFinal;
            console.log('🔧 Using isFinal field from InterviewSessionManager:', {
              isFinal: data.isFinal,
              isPartial: data.isPartial
            });
          } else if (typeof data.isPartial !== 'undefined') {
            // 使用 isPartial 字段（audioProcessor发送的格式）
            data.isFinal = !data.isPartial;
            console.log('🔧 Converting isPartial to isFinal from audioProcessor:', {
              isPartial: data.isPartial,
              isFinal: data.isFinal
            });
          } else {
            // 如果两个字段都没有，默认为最终结果
            data.isFinal = true;
            data.isPartial = false;
            console.log('🔧 No partial/final field found, defaulting to final result:', {
              isFinal: data.isFinal,
              isPartial: data.isPartial
            });
          }
        }

        console.log('✅ Successfully parsed WebSocket message:', {
          messageType: data.type,
          hasText: !!data.text,
          textLength: data.text ? data.text.length : 0,
          textPreview: data.text ? data.text.substring(0, 50) + (data.text.length > 50 ? '...' : '') : 'N/A',
          isFinal: data.isFinal,
          isPartial: data.isPartial,
          timestamp: data.timestamp
        });

        switch (data.type) {
          case 'transcription_partial':
            // 🔥 处理实时转录部分结果
            console.log('🎯 Processing transcription_partial:', {
              text: data.text,
              bubbleId: data.bubbleId,
              service: data.service,
              timestamp: data.timestamp
            });

            // 🔥 使用正确的消息管理方法
            const existingMessage = messages.find(m => m.id === data.bubbleId);

            if (existingMessage) {
              // 更新现有气泡
              updateMessage(data.bubbleId, (msg) => ({
                ...msg,
                content: data.text,
                timestamp: Date.now()
              }));
              console.log('✅ Updated partial transcription bubble:', data.bubbleId);
            } else {
              // 创建新的气泡
              const newMessage = {
                id: data.bubbleId,
                content: data.text,
                type: 'interviewer' as const,
                timestamp: Date.now()
              };
              addMessage(newMessage);
              console.log('✅ Created new partial transcription bubble:', data.bubbleId);
            }
            break;

          case 'transcription_final':
            // 🔥 处理实时转录最终结果
            console.log('🎯 Processing transcription_final:', {
              text: data.text,
              bubbleId: data.bubbleId,
              service: data.service,
              timestamp: data.timestamp
            });

            // 🔥 使用正确的消息管理方法
            const existingFinalMessage = messages.find(m => m.id === data.bubbleId);

            if (existingFinalMessage) {
              // 更新为最终结果
              updateMessage(data.bubbleId, (msg) => ({
                ...msg,
                content: data.text,
                timestamp: Date.now()
              }));
              console.log('✅ Finalized transcription bubble:', data.bubbleId);
            } else {
              // 如果没找到，创建新的最终气泡
              const newMessage = {
                id: data.bubbleId,
                content: data.text,
                type: 'interviewer' as const,
                timestamp: Date.now()
              };
              addMessage(newMessage);
              console.log('✅ Created new final transcription bubble:', data.bubbleId);
            }
            break;

          case 'transcription':
            // 🔥 改进的消息去重机制 - 使用文本内容hash和时间戳（兼容性处理）
            const textHash = btoa(encodeURIComponent(data.text)).substring(0, 20); // 文本内容hash
            const messageId = `${textHash}-${data.isFinal}-${data.timestamp || Date.now()}`;
            const now = Date.now();

            // 🔍 增强诊断：详细的去重检查日志
            console.log('🔍 Transcription message deduplication check:', {
              text: data.text.substring(0, 30) + '...',
              textHash: textHash,
              messageId: messageId.substring(0, 50) + '...',
              isFinal: data.isFinal,
              timestamp: data.timestamp,
              alreadyProcessed: processedMessageIds.current.has(messageId),
              timeSinceLastProcess: now - lastProcessedTime.current,
              processedIdsCount: processedMessageIds.current.size
            });

            if (processedMessageIds.current.has(messageId)) {
              console.log('🔧 Duplicate transcription message filtered (content+timestamp-based):', {
                messageId: messageId.substring(0, 50) + '...',
                text: data.text.substring(0, 30) + '...',
                isFinal: data.isFinal,
                timestamp: data.timestamp,
                reason: 'Same text content and timestamp already processed'
              });
              break; // 跳过重复消息
            }

            // 防抖：如果距离上次处理时间太短，跳过
            if (now - lastProcessedTime.current < 500) { // 增加到500ms防抖，防止重复处理
              console.log('🔧 Transcription message debounced:', {
                timeDiff: now - lastProcessedTime.current,
                text: data.text.substring(0, 30) + '...',
                reason: 'Too soon after last processing'
              });
              break;
            }

            // 记录已处理的消息
            processedMessageIds.current.add(messageId);
            lastProcessedTime.current = now;

            // 清理过期的消息ID（保留最近500个）
            if (processedMessageIds.current.size > 500) {
              const idsArray = Array.from(processedMessageIds.current);
              processedMessageIds.current = new Set(idsArray.slice(-250));
              console.log('🧹 Cleaned processed message IDs cache:', {
                oldSize: idsArray.length,
                newSize: processedMessageIds.current.size
              });
            }

            console.log('🎯 Processing transcription message:', {
              text: data.text,
              isFinal: data.isFinal,
              service: data.service,
              currentTranscriptionId: currentTranscriptionIdRef.current,
              messageId: messageId.substring(0, 50) + '...'
            });

            // ✅ 使用 currentTranscriptionIdRef 管理一段话的生命周期
            if (data.isFinal) {
              console.log('🎯 Processing FINAL transcription result:', {
                text: data.text,
                textLength: data.text.length,
                currentTranscriptionId: currentTranscriptionIdRef.current
              });

              // 🔥 简化架构：只使用MessageFlowManager处理最终结果
              const startTime = Date.now();

              try {
                // 🔍 移除冗余的生命周期管理器，统一使用MessageFlowManager
                console.log('🔍 useInterviewSession: Using simplified MessageFlowManager for final transcription');

                const eventBus = eventBusRef.current!;
                const performanceMonitor = performanceMonitorRef.current!;

                // 🔥 修复：移除重复的LLM触发逻辑 - 后端已在ASR完成后自动调用LLM
                console.log('🔍 useInterviewSession: Final transcription received, backend will auto-trigger LLM', {
                  text: data.text.substring(0, 50) + '...',
                  timestamp: Date.now(),
                  note: 'LLM will be automatically triggered by backend after ASR processing'
                });

                // 🚫 移除重复的LLM触发请求 - 后端interviewWs.ts第1698行已自动处理
                // 原因：前端重复发送llm_trigger可能干扰后端的activeLLMRequests防重复机制
                // 后端在processAndSendCompleteTranscription函数中已经调用getDeepSeekSuggestion

                // 🔥 移除EventBus触发，避免重复处理
                console.log('🔧 Skipping EventBus LLM trigger to avoid duplication');

                // 🔥 修复：直接创建转录消息，避免SmartBubbleManager的复杂逻辑
                const newMessage = {
                  id: generateUniqueId('transcription'),
                  content: data.text,
                  type: 'interviewer' as const,
                  timestamp: Date.now()
                };

                addMessage(newMessage);
                console.log('✅ Created transcription message:', {
                  messageId: newMessage.id,
                  content: newMessage.content.substring(0, 50) + '...',
                  totalMessages: messages.length + 1
                });

                // 最终结果时清空引用
                currentTranscriptionIdRef.current = null;

                // 记录性能指标
                const processingTime = Date.now() - startTime;
                performanceMonitor.recordTranscriptionProcessed(processingTime, true, true);

                console.log('📊 Performance: Final result processed', {
                  processingTime,
                  performanceReport: performanceMonitor.getPerformanceReport()
                });

              } catch (error) {
                console.error('❌ Error processing final result:', error);
                // 🔥 移除EventBus错误触发，避免重复处理
                performanceMonitorRef.current?.recordTranscriptionProcessed(Date.now() - startTime, false, true);

                // 🔥 错误情况下也要清理引用
                currentTranscriptionIdRef.current = null;
              }
            } else {
              console.log('🎯 Processing INTERMEDIATE transcription result:', {
                text: data.text,
                textLength: data.text.length,
                currentTranscriptionId: currentTranscriptionIdRef.current
              });

              // 🔥 使用批处理机制处理中间转录结果
              if (currentTranscriptionIdRef.current) {
                // 更新现有转录消息
                updateMessage(currentTranscriptionIdRef.current, (msg) => ({
                  ...msg,
                  content: data.text,
                  timestamp: Date.now()
                }));
                console.log('✅ Updated intermediate transcription (batched):', currentTranscriptionIdRef.current);
              } else {
                // 创建新的中间转录消息
                const newMessage: Message = {
                  id: generateUniqueId('transcription'),
                  content: data.text,
                  type: 'interviewer',
                  timestamp: Date.now()
                };
                addMessage(newMessage);
                currentTranscriptionIdRef.current = newMessage.id;
                console.log('✅ Created new intermediate transcription (batched):', newMessage.id);
              }
            }
            break;

          case 'final_transcription':
            // 后端已创建新ASR会话并发送完整文本，此事件仅作日志
            console.log('final_transcription (ignored for UI):', data.data);
            break;

          case 'ai_suggestion_chunk':
            // 🔥 简化的AI建议chunk处理 - 移除所有复杂的状态管理
            console.log('🎯 Processing AI suggestion chunk:', {
              chunk: data.text.substring(0, 20) + '...',
              chunkLength: data.text.length,
              timestamp: Date.now()
            });

            // 🔥 查找现有的流式消息
            const existingStreamingMessage = messages.find(msg =>
              msg.type === 'ai-suggestion' && msg.id.startsWith('ai-streaming-')
            );

            if (existingStreamingMessage) {
              // 🔥 更新现有流式消息（使用批处理）
              updateMessage(existingStreamingMessage.id, (msg) => ({
                ...msg,
                content: msg.content + data.text,
                timestamp: Date.now()
              }));

              console.log('🔄 Updated streaming message (batched):', {
                messageId: existingStreamingMessage.id,
                newLength: existingStreamingMessage.content.length + data.text.length,
                chunk: data.text
              });
            } else {
              // 🔥 创建新的流式消息（使用批处理）
              const newMessage: Message = {
                id: generateUniqueId('ai-streaming'),
                content: data.text,
                type: 'ai-suggestion',
                timestamp: Date.now()
              };
              addMessage(newMessage);

              console.log('✅ Created new streaming message (batched):', {
                messageId: newMessage.id,
                content: newMessage.content
              });
            }
            break;

          case 'ai_suggestion_end':
            // 🔥 使用批处理机制处理AI建议end
            console.log('🎯 Processing AI suggestion end (batched)');

            // 🔥 查找流式消息并转换为最终消息
            const streamingMessage = messages.find(msg =>
              msg.type === 'ai-suggestion' && msg.id.startsWith('ai-streaming-')
            );

            if (streamingMessage) {
              updateMessage(streamingMessage.id, (msg) => ({
                ...msg,
                id: msg.id.replace('ai-streaming-', 'ai-suggestion-final-'),
                timestamp: Date.now()
              }));

              console.log('✅ Finalized AI suggestion (batched):', {
                originalId: streamingMessage.id,
                finalId: streamingMessage.id.replace('ai-streaming-', 'ai-suggestion-final-'),
                contentLength: streamingMessage.content.length
              });
            }
            break;

          default:
            // 处理其他消息
            if (typeof lastMessage.data === 'string' && !lastMessage.data.startsWith('{')) {
              // 🔥 优化：过滤掉只包含"."的消息
              const messageContent = lastMessage.data;
              if (messageContent === '.' || messageContent.trim().length === 0) {
                console.log('忽略无意义的消息:', messageContent);
                break;
              }
              
              addMessage({
                id: generateUniqueId('message'),
                content: messageContent,
                type: 'ai-suggestion',
                timestamp: Date.now()
              });
            }
        }
      } catch (error) {
        // 处理JSON解析失败的消息
        console.error('❌ JSON解析失败:', {
          error: error.message,
          messageData: lastMessage.data,
          messageType: typeof lastMessage.data,
          messageLength: lastMessage.data?.length,
          startsWithBrace: lastMessage.data?.startsWith?.('{'),
          endsWithBrace: lastMessage.data?.endsWith?.('}')
        });

        if (typeof lastMessage.data === 'string') {
          const messageContent = lastMessage.data;

          // 🔥 优化：过滤掉只包含"."的消息
          if (messageContent === '.' || messageContent.trim().length === 0) {
            console.log('忽略无意义的非JSON消息:', messageContent);
            return;
          }

          // 🚨 如果消息看起来像JSON格式但解析失败，不要作为ai-suggestion处理
          if (messageContent.startsWith('{') && messageContent.endsWith('}')) {
            console.error('🚨 JSON格式消息解析失败，忽略处理:', {
              content: messageContent.substring(0, 100) + '...',
              length: messageContent.length
            });
            return;
          }

          // 只有非JSON格式的消息才作为ai-suggestion处理
          console.warn('🚨 将非JSON消息作为ai-suggestion处理:', {
            content: messageContent.substring(0, 100) + '...'
          });

          addMessage({
            id: generateUniqueId('message'),
            content: messageContent,
            type: 'ai-suggestion',
            timestamp: Date.now()
          });
        }
      }
    }
  }, [lastMessage]);

  // 🔥 简化：移除复杂的消息一致性验证

  // 🎯 音频质量历史跟踪
  const audioQualityHistoryRef = useRef<number[]>([]);
  const audioStatsRef = useRef({
    totalChunks: 0,
    validChunks: 0,
    avgAmplitude: 0,
    lastValidTime: 0
  });

  // 🚀 增强版自动增益 PCM 转换 - 支持动态增益调整
  const convertFloat32ToPCM16 = useCallback((float32Data: Float32Array): ArrayBuffer => {
    const pcmBuffer = new ArrayBuffer(float32Data.length * 2);
    const view = new DataView(pcmBuffer);

    // 计算当前 chunk 的音频统计
    let maxAmp = 0;
    let rmsAmp = 0;
    let validSamples = 0;

    for (let i = 0; i < float32Data.length; i++) {
      const sample = Math.abs(float32Data[i]);
      maxAmp = Math.max(maxAmp, sample);
      rmsAmp += sample * sample;
      if (sample > 0.00001) validSamples++; // 过滤极小噪声
    }

    rmsAmp = Math.sqrt(rmsAmp / float32Data.length);

    // 🎯 动态增益参数 - 根据历史数据调整
    const MIN_TARGET_AMP = 0.005;  // 降低目标最低幅度，提高敏感度
    const MAX_GAIN = 100;          // 提高最大增益
    const HISTORY_WEIGHT = 0.3;    // 历史数据权重

    // 更新音频质量历史
    audioQualityHistoryRef.current.push(maxAmp);
    if (audioQualityHistoryRef.current.length > 10) {
      audioQualityHistoryRef.current.shift(); // 保持最近10个样本
    }

    // 计算历史平均幅度
    const avgHistoryAmp = audioQualityHistoryRef.current.length > 0
      ? audioQualityHistoryRef.current.reduce((a, b) => a + b, 0) / audioQualityHistoryRef.current.length
      : maxAmp;

    // 🎯 智能增益计算 - 结合当前和历史数据
    let gain = 1;
    if (maxAmp > 0) {
      const targetAmp = Math.max(MIN_TARGET_AMP, avgHistoryAmp * HISTORY_WEIGHT);
      if (maxAmp < targetAmp) {
        gain = Math.min(targetAmp / maxAmp, MAX_GAIN);

        // 🔥 额外增益策略：对极低音量应用更激进的增益
        if (maxAmp < 0.001) {
          gain = Math.min(gain * 2, MAX_GAIN);
        }
      }
    }

    // 转换并应用增益
    for (let i = 0; i < float32Data.length; i++) {
      let sample = float32Data[i] * gain;
      sample = Math.max(-1, Math.min(1, sample)); // 剪裁防削波
      const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
      view.setInt16(i * 2, intSample, true);
    }

    // 🎯 更新音频统计
    audioStatsRef.current.totalChunks++;
    if (maxAmp > 0.001) {
      audioStatsRef.current.validChunks++;
      audioStatsRef.current.lastValidTime = Date.now();
    }
    audioStatsRef.current.avgAmplitude =
      (audioStatsRef.current.avgAmplitude * (audioStatsRef.current.totalChunks - 1) + maxAmp) /
      audioStatsRef.current.totalChunks;

    return pcmBuffer;
  }, []);

  // 🎯 音频缓冲队列管理
  const audioBufferRef = useRef<Float32Array[]>([]);
  const lastSendTimeRef = useRef<number>(0);
  const audioQualityThresholdRef = useRef({
    minAmplitude: 0.001,      // 最小有效幅度
    minValidRatio: 0.1,       // 最小有效样本比例
    minBufferSize: 3,         // 最小缓冲区大小
    maxBufferSize: 10,        // 最大缓冲区大小
    sendInterval: 1000        // 发送间隔（毫秒）
  });

  // 🎯 音频质量评分算法
  const calculateAudioQuality = useCallback((audioData: Float32Array): number => {
    let maxAmplitude = 0;
    let rmsAmplitude = 0;
    let validSamples = 0;
    let energySum = 0;

    for (let i = 0; i < audioData.length; i++) {
      const sample = Math.abs(audioData[i]);
      maxAmplitude = Math.max(maxAmplitude, sample);
      rmsAmplitude += sample * sample;
      energySum += sample;

      if (sample > 0.00001) { // 过滤极小噪声
        validSamples++;
      }
    }

    rmsAmplitude = Math.sqrt(rmsAmplitude / audioData.length);
    const validRatio = validSamples / audioData.length;
    const avgEnergy = energySum / audioData.length;

    // 🎯 综合质量评分 (0-100)
    let qualityScore = 0;

    // 幅度评分 (40%)
    if (maxAmplitude > 0.01) qualityScore += 40;
    else if (maxAmplitude > 0.001) qualityScore += 20;
    else if (maxAmplitude > 0.0001) qualityScore += 10;

    // 有效样本比例评分 (30%)
    qualityScore += validRatio * 30;

    // RMS能量评分 (20%)
    if (rmsAmplitude > 0.005) qualityScore += 20;
    else if (rmsAmplitude > 0.001) qualityScore += 10;

    // 平均能量评分 (10%)
    if (avgEnergy > 0.001) qualityScore += 10;
    else if (avgEnergy > 0.0001) qualityScore += 5;

    return Math.min(100, qualityScore);
  }, []);

  // 🔥 智能音频处理 - 添加质量检测和缓冲机制
  const handleAudioData = useCallback((audioData: Float32Array) => {
    if (!sendWebSocketMessage || !isListening) {
      console.log('🚫 音频处理被跳过:', {
        hasSendWebSocketMessage: !!sendWebSocketMessage,
        isListening,
        readyState,
        socketUrl: socketUrl ? 'set' : 'null'
      });
      return;
    }

    try {
      // 🎯 计算音频质量评分
      const qualityScore = calculateAudioQuality(audioData);
      const threshold = audioQualityThresholdRef.current;

      // 🎯 基础质量检查
      let maxAmplitude = 0;
      let validSamples = 0;
      for (let i = 0; i < audioData.length; i++) {
        const sample = Math.abs(audioData[i]);
        maxAmplitude = Math.max(maxAmplitude, sample);
        if (sample > 0.00001) validSamples++;
      }

      const validRatio = validSamples / audioData.length;
      const isHighQuality = qualityScore >= 30 && maxAmplitude >= threshold.minAmplitude && validRatio >= threshold.minValidRatio;

      // 音频质量检查完成

      // 🎯 智能缓冲策略
      if (isHighQuality) {
        audioBufferRef.current.push(audioData);

        // 限制缓冲区大小
        if (audioBufferRef.current.length > threshold.maxBufferSize) {
          audioBufferRef.current.shift();
        }
      }

      const now = Date.now();
      const shouldSend = (
        audioBufferRef.current.length >= threshold.minBufferSize &&
        (now - lastSendTimeRef.current) >= threshold.sendInterval
      ) || audioBufferRef.current.length >= threshold.maxBufferSize;

      if (shouldSend && audioBufferRef.current.length > 0) {
        // 🎯 合并缓冲区中的音频数据
        const totalLength = audioBufferRef.current.reduce((sum, chunk) => sum + chunk.length, 0);
        const mergedAudio = new Float32Array(totalLength);
        let offset = 0;

        for (const chunk of audioBufferRef.current) {
          mergedAudio.set(chunk, offset);
          offset += chunk.length;
        }

        // 转换为16位PCM
        const pcmData = convertFloat32ToPCM16(mergedAudio);

        // 🎯 最终质量验证
        const pcmView = new DataView(pcmData);
        let pcmMaxAmplitude = 0;
        for (let i = 0; i < pcmData.byteLength; i += 2) {
          const sample = Math.abs(pcmView.getInt16(i, true));
          pcmMaxAmplitude = Math.max(pcmMaxAmplitude, sample);
        }

        // 只发送高质量的音频数据
        if (pcmMaxAmplitude > 100) { // PCM阈值
          console.log('🎵 发送高质量音频:', {
            chunks: audioBufferRef.current.length,
            totalSize: pcmData.byteLength,
            maxAmplitude: pcmMaxAmplitude,
            qualityScore: qualityScore.toFixed(1)
          });

          sendWebSocketMessage(pcmData);
          lastSendTimeRef.current = now;
        }

        // 清空缓冲区
        audioBufferRef.current = [];
      }

    } catch (error) {
      console.error('❌ 处理音频数据失败:', error);
    }
  }, [sendWebSocketMessage, convertFloat32ToPCM16, isListening, calculateAudioQuality]);

  // 🔥 简化的音频处理逻辑
  useEffect(() => {
    console.log('🔍 音频处理器useEffect触发:', {
      hasSharedStream: !!interviewConfig.sharedStream,
      readyState: readyState,
      audioTracks: interviewConfig.sharedStream?.getAudioTracks().length || 0
    });

    if (interviewConfig.sharedStream && readyState === 1) {
      console.log('🎵 开始设置音频处理');
      const audioTracks = interviewConfig.sharedStream.getAudioTracks();

      if (audioTracks.length > 0) {
        const audioOnlyStream = new MediaStream(audioTracks);

        // 🔥 新增：音频轨道状态监控
        console.log('🎵 音频轨道详细信息:', {
          trackCount: audioTracks.length,
          tracks: audioTracks.map((track, index) => ({
            index,
            id: track.id,
            label: track.label,
            kind: track.kind,
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState
          }))
        });

        // 🔥 新增：监听音频轨道状态变化
        audioTracks.forEach((track, index) => {
          track.onended = () => {
            console.warn(`⚠️ 音频轨道 ${index} 已结束:`, track.label);
          };
          track.onmute = () => {
            console.warn(`⚠️ 音频轨道 ${index} 已静音:`, track.label);
          };
          track.onunmute = () => {
            console.log(`🔊 音频轨道 ${index} 取消静音:`, track.label);
          };
        });

        try {
          // 创建Web Audio API上下文
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
            sampleRate: 16000
          });

          console.log('🎵 AudioContext创建成功:', {
            sampleRate: audioContext.sampleRate,
            state: audioContext.state,
            baseLatency: audioContext.baseLatency,
            outputLatency: audioContext.outputLatency
          });

          // 创建音频源
          const source = audioContext.createMediaStreamSource(audioOnlyStream);

          console.log('🎵 音频源创建成功:', {
            mediaStream: audioOnlyStream,
            streamActive: audioOnlyStream.active,
            streamId: audioOnlyStream.id
          });

          // 创建ScriptProcessorNode来处理音频数据
          const bufferSize = 4096;
          const processor = audioContext.createScriptProcessor(bufferSize, 1, 1);

          // 连接音频节点
          source.connect(processor);
          processor.connect(audioContext.destination);

          // 保存引用以便停止时清理
          audioContextRef.current = audioContext;
          processorRef.current = processor;
          sourceRef.current = source;

          // 🎯 优化的音频处理器 - 减少冗余日志，专注质量监控
          let processingCount = 0;
          processor.onaudioprocess = (event) => {
            if (!isListening) {
              return;
            }

            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // 复制音频数据
            const audioData = new Float32Array(inputData.length);
            audioData.set(inputData);

            // 🎯 简化的质量检查 - 只在必要时输出日志
            processingCount++;
            if (processingCount % 50 === 0) { // 每50次处理输出一次状态
              let maxAmplitude = 0;
              for (let i = 0; i < audioData.length; i++) {
                maxAmplitude = Math.max(maxAmplitude, Math.abs(audioData[i]));
              }

              console.log('🎤 音频处理状态:', {
                count: processingCount,
                maxAmplitude: maxAmplitude.toFixed(6),
                bufferSize: audioData.length,
                stats: audioStatsRef.current
              });
            }

            // 直接处理音频数据
            handleAudioData(audioData);
          };

          console.log('✅ 音频处理器设置完成');

        } catch (error) {
          console.error('❌ 设置音频处理失败:', error);
        }
      }
    }

    return () => {
      // 清理Web Audio API资源
      if (processorRef.current) {
        processorRef.current.onaudioprocess = null;
        processorRef.current.disconnect();
        processorRef.current = null;
      }

      if (sourceRef.current) {
        sourceRef.current.disconnect();
        sourceRef.current = null;
      }

      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
    };
  }, [interviewConfig.sharedStream, readyState, handleAudioData]);

  // 更新计时器
  useEffect(() => {
    if (!session.isActive) return;

    const intervalId = setInterval(() => {
      const now = Date.now();
      const elapsed = now - session.startTime;
      updateSession((prev) => ({ ...prev, elapsedTime: elapsed }));
      setFormattedTime(formatElapsedTime(elapsed));
    }, 1000);

    return () => clearInterval(intervalId);
  }, [session.isActive, session.startTime]);

  // 🔥 真实音频处理逻辑 - 使用Zustand store的addMessage，不再需要本地函数

  const toggleListening = useCallback(() => {
    setIsListening(prev => {
      const newState = !prev;
      console.log('🎙️ 切换监听状态:', {
        from: prev,
        to: newState,
        timestamp: new Date().toISOString()
      });
      return newState;
    });
  }, []);

  // 🎯 音频处理性能监控
  useEffect(() => {
    if (!isListening) return;

    const monitoringInterval = setInterval(() => {
      const stats = audioStatsRef.current;
      const now = Date.now();
      const timeSinceLastValid = now - stats.lastValidTime;

      console.log('📊 音频处理统计:', {
        totalChunks: stats.totalChunks,
        validChunks: stats.validChunks,
        validRate: stats.totalChunks > 0 ? (stats.validChunks / stats.totalChunks * 100).toFixed(1) + '%' : '0%',
        avgAmplitude: stats.avgAmplitude.toFixed(6),
        timeSinceLastValid: timeSinceLastValid > 5000 ? `${(timeSinceLastValid / 1000).toFixed(1)}s` : 'recent',
        bufferSize: audioBufferRef.current.length,
        qualityHistory: audioQualityHistoryRef.current.length
      });
    }, 10000); // 每10秒输出一次统计

    return () => clearInterval(monitoringInterval);
  }, [isListening]);

  // 🔥 监控isListening状态变化
  useEffect(() => {
    console.log('🎙️ isListening状态变化:', {
      isListening,
      timestamp: new Date().toISOString(),
      hasWebSocket: !!sendWebSocketMessage,
      readyState
    });

    // 重置统计数据
    if (isListening) {
      audioStatsRef.current = {
        totalChunks: 0,
        validChunks: 0,
        avgAmplitude: 0,
        lastValidTime: Date.now()
      };
      audioQualityHistoryRef.current = [];
      audioBufferRef.current = [];
      lastSendTimeRef.current = 0;
    }
  }, [isListening, sendWebSocketMessage, readyState]);

  const endInterview = useCallback(() => {
    // 停止面试会话
    updateSession((prev) => ({ ...prev, isActive: false }));
    setIsListening(false);

    // 清理屏幕共享资源
    if (interviewConfig.sharedStream) {
      console.log('Ending interview: Stopping screen share stream');
      interviewConfig.sharedStream.getTracks().forEach(track => {
        track.stop();
        console.log(`Stopped ${track.kind} track:`, track.label);
      });

      // 更新Zustand store状态
      setSharedStream(null);
      setScreenShareStatus('idle');
      console.log('Screen share cleaned up on interview end');
    }

    // 面试结束后导航到面试记录页面
    console.log('Interview ended, navigating to interview records page');
    navigate('/ai-interview?tab=records');
  }, [interviewConfig.sharedStream, setSharedStream, setScreenShareStatus, navigate]);

  const refreshSession = useCallback(async () => {
    console.log('开始刷新面试会话...');

    // 停止当前的语音识别
    setIsListening(false);

    // 清理屏幕共享资源（如果存在）
    if (interviewConfig.sharedStream) {
      interviewConfig.sharedStream.getTracks().forEach(track => {
        track.stop();
      });
    }

    // 保持当前会话信息，只清空消息历史
    setMessages([]);

    // 清理Zustand store状态
    setSharedStream(null);
    setScreenShareStatus('idle');

    // 重新获取音频流（根据模式选择音频源）
    try {
      setScreenShareStatus('pending');

      const stream = await acquireAudioStream();

      // 监听用户手动停止共享的事件（仅在live模式下监听视频轨道）
      if (mode === 'live' && stream.getVideoTracks()[0]) {
        stream.getVideoTracks()[0].onended = () => {
          console.log('🖥️ Screen share ended by browser UI.');
          setSharedStream(null);
          setScreenShareStatus('idle');
        };
      }

      // 监听音频轨道结束事件（两种模式都需要）
      if (stream.getAudioTracks()[0]) {
        stream.getAudioTracks()[0].onended = () => {
          const audioContext = mode === 'mock' ? '麦克风' : '系统音频';
          console.log(`🎤 ${audioContext} ended by browser or device.`);
          setSharedStream(null);
          setScreenShareStatus('idle');
        };
      }

      setSharedStream(stream);
      setScreenShareStatus('sharing');

      // 重新启动语音识别
      setIsListening(true);

      console.log('面试会话刷新完成');

    } catch (error) {
      console.error('重新获取屏幕共享失败:', error);
      setScreenShareStatus('denied');
    }
  }, [interviewConfig.sharedStream, setSharedStream, setScreenShareStatus]);

  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptionEvents, setTranscriptionEvents] = useState<TranscriptionEvent[]>([]);
  const [audioStats, setAudioStats] = useState<AudioStats>({
    segmentCount: 0,
    processingLatency: 0,
    averageQuality: 0,
    speechDetectionRate: 0
  });

  const wsRef = useRef<WebSocket | null>(null);
  const audioBufferManagerRef = useRef<AudioBufferManager | null>(null);
  const vadProcessorRef = useRef<VADProcessor | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const processorNodeRef = useRef<ScriptProcessorNode | null>(null);

  // 初始化音频处理器
  const initializeAudioProcessors = useCallback(async () => {
    try {
      audioBufferManagerRef.current = new AudioBufferManager(config.audioConfig);
      
      if (config.enableVAD) {
        vadProcessorRef.current = new VADProcessor();
        await vadProcessorRef.current.initialize();
      }
      
      console.log('✅ Audio processors initialized');
    } catch (error) {
      console.error('❌ Failed to initialize audio processors:', error);
    }
  }, [config.audioConfig, config.enableVAD]);

  // 连接WebSocket
  const connect = useCallback(async () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    try {
      await initializeAudioProcessors();
      
      wsRef.current = new WebSocket(config.wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected');
        setIsConnected(true);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('❌ WebSocket disconnected');
        setIsConnected(false);
        setIsRecording(false);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  }, [config.wsUrl, initializeAudioProcessors]);

  // 处理WebSocket消息
  const handleWebSocketMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'transcription':
        const event: TranscriptionEvent = {
          id: `${Date.now()}-${Math.random()}`,
          text: data.text || '',
          speaker: data.speaker || 'interviewer',
          timestamp: data.timestamp || Date.now(),
          confidence: data.confidence,
          isFinal: !data.isPartial
        };
        
        setTranscriptionEvents(prev => {
          const updated = [...prev, event];
          return updated.slice(-50); // 保持最近50个事件
        });
        break;
        
      case 'asr_error':
        console.error('ASR Error:', data.message);
        break;

      case 'mock_interview_question':
        if (mode === 'mock') {
          console.log('🤖 Received AI mock interview question:', data.questionText);
          // 添加AI问题到消息列表
          addMessage({
            id: data.questionId,
            content: data.questionText,
            type: 'interviewer', // 在mock模式下，interviewer表示AI问题
            timestamp: data.timestamp || Date.now()
          });
        }
        break;

      case 'mock_interview_feedback':
        if (mode === 'mock') {
          console.log('📊 Received AI mock interview feedback:', data.feedback);
          // 可以在这里处理反馈显示逻辑
        }
        break;

      case 'mock_interview_session_start':
        if (mode === 'mock') {
          console.log('🎯 Mock interview session started:', data.sessionInfo);
        }
        break;

      case 'mock_interview_session_end':
        if (mode === 'mock') {
          console.log('🏁 Mock interview session ended');
        }
        break;

      default:
        console.log('Unhandled message type:', data.type);
    }
  }, []);

  // 生成音频哈希
  const generateAudioHash = useCallback((audioData: Float32Array): string => {
    const dataString = Array.from(audioData.slice(0, Math.min(1000, audioData.length))).join(',');
    return CryptoJS.MD5(dataString).toString();
  }, []);

  // 发送音频数据
  const sendAudioData = useCallback((audioData: Float32Array) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      const audioHash = generateAudioHash(audioData);
      
      // 转换为Base64
      const buffer = new ArrayBuffer(audioData.length * 4);
      const view = new Float32Array(buffer);
      view.set(audioData);
      const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));

      const message = {
        type: 'pcm_audio_chunk',
        payload: base64,
        format: 'pcm',
        sampleRate: 16000,
        channels: 1,
        audioHash: audioHash,
        timestamp: Date.now()
      };

      wsRef.current.send(JSON.stringify(message));
      
      // 更新统计
      setAudioStats(prev => ({
        ...prev,
        segmentCount: prev.segmentCount + 1
      }));
    } catch (error) {
      console.error('Error sending audio data:', error);
    }
  }, [generateAudioHash]);

  // 开始录音
  const startRecording = useCallback(async () => {
    if (isRecording) return;

    try {
      // 使用音频源抽象获取流
      const stream = await acquireAudioStream();

      mediaStreamRef.current = stream;
      audioContextRef.current = new AudioContext({ sampleRate: 16000 });
      
      const source = audioContextRef.current.createMediaStreamSource(stream);
      const processor = audioContextRef.current.createScriptProcessor(4096, 1, 1);
      
      processor.onaudioprocess = (event) => {
        const audioData = event.inputBuffer.getChannelData(0);
        
        if (audioBufferManagerRef.current) {
          // VAD处理
          let vadResult: VADResult = { isSpeech: true, confidence: 0.8, energy: 0, timestamp: Date.now() };
          if (vadProcessorRef.current) {
            vadResult = vadProcessorRef.current.process(audioData);
          }
          
          // 添加到缓冲管理器
          const triggerInfo = audioBufferManagerRef.current.addAudioChunk(audioData, vadResult);
          
          if (triggerInfo.shouldTrigger) {
            const recognitionBuffer = audioBufferManagerRef.current.getRecognitionBuffer();
            if (recognitionBuffer) {
              sendAudioData(recognitionBuffer);
            }
          }
        }
      };

      source.connect(processor);
      processor.connect(audioContextRef.current.destination);
      processorNodeRef.current = processor;

      setIsRecording(true);
      console.log('✅ Recording started');
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  }, [isRecording, sendAudioData]);

  // 停止录音
  const stopRecording = useCallback(() => {
    if (!isRecording) return;

    if (processorNodeRef.current) {
      processorNodeRef.current.disconnect();
      processorNodeRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    setIsRecording(false);
    console.log('⏹️ Recording stopped');
  }, [isRecording]);

  // 断开连接
  const disconnect = useCallback(() => {
    stopRecording();
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setTranscriptionEvents([]);
    setAudioStats({
      segmentCount: 0,
      processingLatency: 0,
      averageQuality: 0,
      speechDetectionRate: 0
    });
  }, [stopRecording]);

  // 🔥 发送AI模拟面试回答
  const sendMockInterviewAnswer = useCallback((questionId: string, answerText: string, answerDuration: number, confidence: number = 0.8) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, cannot send mock interview answer');
      return;
    }

    if (mode !== 'mock') {
      console.warn('Not in mock mode, cannot send mock interview answer');
      return;
    }

    const answerMessage = {
      type: 'mock_interview_answer',
      mode: 'mock',
      sessionId: session.id,
      questionId,
      answerText,
      answerDuration,
      confidence,
      timestamp: Date.now()
    };

    wsRef.current.send(JSON.stringify(answerMessage));
    console.log('📤 Sent mock interview answer:', { questionId, answerLength: answerText.length });

    // 添加用户回答到消息列表
    addMessage({
      id: `answer-${questionId}-${Date.now()}`,
      content: answerText,
      type: 'ai-suggestion', // 在mock模式下，ai-suggestion表示用户回答
      timestamp: Date.now()
    });
  }, [mode, session.id, addMessage]);

  // 清理资源
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    session,
    formattedTime,
    isListening,
    toggleListening,
    refreshSession,
    endInterview,
    // WebSocket状态
    connectionStatus: readyState,
    isConnected,
    isRecording,
    transcriptionEvents,
    audioStats,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    clearTranscriptions: () => setTranscriptionEvents([]),
    getLatestTranscription: () => transcriptionEvents[transcriptionEvents.length - 1]?.text || '',
    getAudioProcessorReady: () => !!audioBufferManagerRef.current,
    // AI模拟面试专用功能
    sendMockInterviewAnswer
  };
}
