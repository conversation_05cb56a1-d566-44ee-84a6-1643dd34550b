// 积分服务 - 重构版：事务性原子操作
import prisma from '../lib/prisma.js';
import { UsageType } from '@prisma/client';
import { logger } from '../utils/logger.js';

// 面试成本常量
export const MOCK_INTERVIEW_COST = 1;  // 模拟面试消耗1个积分
export const FORMAL_INTERVIEW_COST = 1; // 正式面试消耗1个积分

/**
 * 积分服务类 - 重构版
 * 核心设计理念：在数据库事务中原子性地完成检查和扣费
 */
export class CreditsService {
  private static instance: CreditsService;

  public static getInstance(): CreditsService {
    if (!CreditsService.instance) {
      CreditsService.instance = new CreditsService();
    }
    return CreditsService.instance;
  }

  /**
   * 【核心】检查并扣除积分的原子操作
   * 这个方法在数据库事务中原子性地完成检查和扣费
   * @param tx - Prisma的事务客户端
   * @param userId - 用户ID
   * @param interviewType - 'mock' 或 'formal'
   * @throws 如果积分不足或更新失败，则抛出错误
   */
  public async checkAndDeduct(tx: any, userId: string, interviewType: 'mock' | 'formal'): Promise<void> {
    const cost = interviewType === 'mock' ? MOCK_INTERVIEW_COST : FORMAL_INTERVIEW_COST;
    const fieldName = interviewType === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';

    logger.info(`[CreditsService] Checking and deducting ${cost} credits for ${interviewType} interview, user: ${userId}`);

    // 在事务中查询用户余额
    const userBalance = await tx.userBalance.findUnique({
      where: { userId },
      select: { [fieldName]: true, updatedAt: true }
    });

    if (!userBalance || (userBalance as any)[fieldName] < cost) {
      logger.warn(`[CreditsService] Insufficient credits for user ${userId}: required ${cost}, available ${userBalance ? (userBalance as any)[fieldName] : 0}`);
      throw new Error('INSUFFICIENT_CREDITS');
    }

    // 在事务中扣费
    await tx.userBalance.update({
      where: {
        userId,
        updatedAt: userBalance.updatedAt // 乐观锁
      },
      data: {
        [fieldName]: {
          decrement: cost
        }
      }
    });

    // 在事务中创建消费记录
    await tx.usageRecord.create({
      data: {
        userId,
        type: interviewType === 'mock' ? UsageType.MOCK_INTERVIEW : UsageType.FORMAL_INTERVIEW,
        amount: -cost, // 负数表示消耗
        reason: `AI${interviewType === 'mock' ? '模拟' : '正式'}面试消耗`,
      }
    });

    logger.info(`[CreditsService] Successfully deducted ${cost} credits for user ${userId}`);
  }

  /**
   * 【便捷方法】快速检查用户是否有足够积分（不扣费）
   * @param userId - 用户ID
   * @param interviewType - 'mock' 或 'formal'
   * @returns 如果积分足够，返回 true，否则返回 false
   */
  public async hasEnoughCredits(userId: string, interviewType: 'mock' | 'formal'): Promise<boolean> {
    try {
      const cost = interviewType === 'mock' ? MOCK_INTERVIEW_COST : FORMAL_INTERVIEW_COST;
      const fieldName = interviewType === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';

      const userBalance = await prisma.userBalance.findUnique({
        where: { userId },
        select: { [fieldName]: true }
      });

      if (!userBalance || (userBalance as any)[fieldName] < cost) {
        logger.info(`[CreditsService] User ${userId} has insufficient ${interviewType} interview credits`);
        return false;
      }

      logger.info(`[CreditsService] User ${userId} has sufficient ${interviewType} interview credits: ${(userBalance as any)[fieldName]}`);
      return true;
    } catch (error) {
      logger.error(`[CreditsService] Error checking credits for user ${userId}:`, error);
      return false;
    }
  }
}

// 导出单例实例
export const creditsService = CreditsService.getInstance();
