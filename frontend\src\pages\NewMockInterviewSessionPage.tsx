import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToastContext } from '../contexts/ToastContext';
import useInterviewStore, { InterviewMessage } from '../stores/interviewStore';
import useDocumentTitle from '../hooks/useDocumentTitle';

const NewMockInterviewSessionPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('AI模拟面试');

  const navigate = useNavigate();
  const location = useLocation();
  const { showError, showSuccess } = useToastContext();
  
  // 从新的store中获取状态和方法
  const messages = useInterviewStore((state) => state.messages);
  const connectionStatus = useInterviewStore((state) => state.connectionStatus);
  const currentQuestionId = useInterviewStore((state) => state.currentQuestionId);
  const { startInterview, sendAnswer, disconnect } = useInterviewStore((state) => ({
    startInterview: state.startInterview,
    sendAnswer: state.sendAnswer,
    disconnect: state.disconnect
  }));

  // 本地状态
  const [userInput, setUserInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 页面加载时自动启动面试
  useEffect(() => {
    if (location.state && connectionStatus === 'disconnected') {
      console.log('🎯 [FINAL-FIX] NewMockInterviewSessionPage: Auto-starting interview');
      
      const config = {
        companyName: location.state.companyName || '模拟公司',
        positionName: location.state.positionName || '模拟岗位',
        jobDescription: `${location.state.positionName || '模拟岗位'} - ${location.state.companyName || '模拟公司'}`,
        mode: 'mock' as const,
        interviewLanguage: location.state.language || 'chinese' as const,
        answerStyle: location.state.answerStyle || 'conversational' as const
      };

      setIsLoading(true);
      startInterview(config).finally(() => {
        setIsLoading(false);
      });
    }
  }, [location.state, connectionStatus, startInterview]);

  // 组件卸载时断开连接
  useEffect(() => {
    return () => {
      console.log('🧹 NewMockInterviewSessionPage: Cleaning up, disconnecting...');
      disconnect();
    };
  }, [disconnect]);

  // 发送用户回答
  const handleSendAnswer = () => {
    if (!userInput.trim()) {
      showError('请输入您的回答');
      return;
    }

    if (connectionStatus !== 'connected') {
      showError('连接未建立，请稍后重试');
      return;
    }

    console.log('📤 [FINAL-FIX] Sending user answer:', userInput);
    sendAnswer(userInput.trim());
    setUserInput(''); // 清空输入框
  };

  // 结束面试
  const handleEndInterview = () => {
    disconnect();
    navigate('/');
  };

  // 渲染连接状态
  const renderConnectionStatus = () => {
    const statusConfig = {
      disconnected: { text: '未连接', color: 'text-gray-500' },
      connecting: { text: '连接中...', color: 'text-yellow-500' },
      connected: { text: '已连接', color: 'text-green-500' },
      error: { text: '连接错误', color: 'text-red-500' }
    };

    const config = statusConfig[connectionStatus];
    return (
      <div className={`text-sm ${config.color} mb-4`}>
        连接状态: {config.text}
      </div>
    );
  };

  // 渲染消息列表
  const renderMessages = () => {
    if (messages.length === 0) {
      return (
        <div className="text-center text-gray-500 py-8">
          {isLoading ? '正在启动面试，请稍候...' : '等待面试开始...'}
        </div>
      );
    }

    return messages.map((message: InterviewMessage) => (
      <div
        key={message.id}
        className={`mb-4 p-4 rounded-lg ${
          message.sender === 'ai'
            ? 'bg-blue-50 border-l-4 border-blue-400'
            : 'bg-green-50 border-l-4 border-green-400'
        }`}
      >
        <div className="flex justify-between items-start mb-2">
          <span className="font-semibold text-sm">
            {message.sender === 'ai' ? '🤖 AI面试官' : '👤 您的回答'}
          </span>
          <span className="text-xs text-gray-500">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
        </div>
        <div className="whitespace-pre-wrap text-gray-800">
          {message.text}
        </div>
      </div>
    ));
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-semibold text-gray-800">AI模拟面试</h1>
            <p className="text-sm text-gray-600">
              {location.state?.positionName} - {location.state?.companyName}
            </p>
          </div>
          <button
            onClick={handleEndInterview}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            结束面试
          </button>
        </div>
        {renderConnectionStatus()}
      </div>

      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto px-6 py-4">
        <div className="max-w-4xl mx-auto">
          {renderMessages()}
        </div>
      </div>

      {/* 输入区域 */}
      <div className="bg-white border-t px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-4">
            <textarea
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              placeholder="请输入您的回答..."
              className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              disabled={connectionStatus !== 'connected'}
            />
            <button
              onClick={handleSendAnswer}
              disabled={connectionStatus !== 'connected' || !userInput.trim()}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              发送回答
            </button>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {connectionStatus === 'connected' 
              ? '请输入您的回答，然后点击发送' 
              : '等待连接建立...'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewMockInterviewSessionPage;
