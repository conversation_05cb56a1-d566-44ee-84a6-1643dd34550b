// 统一WebSocket管理器 - 完全重构版
// 解决连接泄漏和重复连接问题

import { connectionTracker } from '../utils/connectionTracker';

/**
 * 获取正确的 WebSocket 服务基础 URL
 * @returns {string} WebSocket base URL (e.g., wss://mianshijun.xyz or ws://localhost:3000)
 */
function getWsBaseUrl(): string {
  // 生产环境的判断：优先检查 hostname 是否为生产域名
  if (window.location.hostname === 'mianshijun.xyz' || import.meta.env.PROD) {
    const protocol = 'wss:';
    const host = window.location.host; // 这将是 'mianshijun.xyz'
    return `${protocol}//${host}`;
  }

  // 默认返回开发环境地址
  return 'ws://localhost:3000';
}

export interface InterviewConfig {
  interviewType: 'mock' | 'formal';
  interviewLanguage: 'zh' | 'en';
  answerStyle: 'concise' | 'detailed';
  selectedPositionId?: string;
}

export interface WebSocketMessage {
  type: string;
  sessionId?: string;
  data?: any;
  timestamp?: number;
}

/**
 * 统一WebSocket管理器 - 单例模式
 * 确保整个应用只有一个WebSocket连接
 */
export class UnifiedWebSocketManager {
  private static instance: UnifiedWebSocketManager | null = null;
  private ws: WebSocket | null = null;
  private currentSessionId: string | null = null;
  private connectionState: 'disconnected' | 'connecting' | 'connected' | 'error' = 'disconnected';
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;

  private constructor() {
    // 私有构造函数，确保单例
    console.log('🚀 UnifiedWebSocketManager: Singleton instance created');
    console.log('🎯 权威单例连接架构：WebSocket管理器初始化完成');

    // 初始化状态
    this.connectionState = 'disconnected';
    this.messageQueue = [];
    this.eventListeners = new Map();
    this.reconnectAttempts = 0;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UnifiedWebSocketManager {
    if (!UnifiedWebSocketManager.instance) {
      UnifiedWebSocketManager.instance = new UnifiedWebSocketManager();
    }
    return UnifiedWebSocketManager.instance;
  }

  /**
   * 连接到WebSocket - 强制单连接模式
   */
  public async connect(sessionId: string, config: InterviewConfig): Promise<void> {
    console.log(`🚀 UnifiedWebSocketManager: Connecting to session ${sessionId} (FORCE SINGLE CONNECTION)`);

    // 如果已有连接且是同一个session，直接返回
    if (this.ws && this.currentSessionId === sessionId && this.connectionState === 'connected') {
      console.log(`✅ UnifiedWebSocketManager: Already connected to session ${sessionId}`);
      return;
    }

    // 强制关闭任何现有连接
    await this.forceDisconnect();

    // 创建新连接
    await this.createConnection(sessionId, config);
  }

  /**
   * 强制断开现有连接
   */
  private async forceDisconnect(): Promise<void> {
    if (this.ws) {
      console.log(`🔌 UnifiedWebSocketManager: Force closing existing connection`);
      
      // 移除事件监听器
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onclose = null;
      this.ws.onerror = null;

      // 强制关闭连接
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        this.ws.close(1000, 'Force disconnect for new connection');
      }

      this.ws = null;
      this.currentSessionId = null;
      this.connectionState = 'disconnected';
    }
  }

  /**
   * 创建新的WebSocket连接
   */
  private async createConnection(sessionId: string, config: InterviewConfig): Promise<void> {
    this.connectionState = 'connecting';
    this.currentSessionId = sessionId;

    try {
      const wsUrl = this.buildWebSocketUrl(sessionId);
      console.log(`🔗 UnifiedWebSocketManager: Creating connection to ${wsUrl}`);

      // 追踪连接创建
      connectionTracker.trackConnection(
        sessionId,
        'websocket',
        'UnifiedWebSocketManager.createConnection',
        { url: wsUrl, timestamp: Date.now() }
      );

      this.ws = new WebSocket(wsUrl);

      // 设置事件处理器
      this.setupEventHandlers(sessionId, config);

      // 等待连接建立
      await this.waitForConnection();

      console.log(`✅ UnifiedWebSocketManager: Connected to session ${sessionId}`);
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;

      // 发送初始化消息
      this.sendInitializationMessage(config);

    } catch (error) {
      console.error(`❌ UnifiedWebSocketManager: Connection failed for ${sessionId}:`, error);
      this.connectionState = 'error';
      connectionTracker.updateConnectionStatus(sessionId, 'error', { error: error.toString() });
      throw error;
    }
  }

  /**
   * 设置WebSocket事件处理器
   */
  private setupEventHandlers(sessionId: string, config: InterviewConfig): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log(`🔗 UnifiedWebSocketManager: Connection opened for ${sessionId}`);
      this.connectionState = 'connected';
      connectionTracker.updateConnectionStatus(sessionId, 'connected');

      // 发射连接状态变化事件
      window.dispatchEvent(new CustomEvent('websocket-connection-change', {
        detail: { sessionId, connected: true }
      }));
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event);
    };

    this.ws.onclose = (event) => {
      console.log(`🔌 UnifiedWebSocketManager: Connection closed for ${sessionId}:`, event);
      this.connectionState = 'disconnected';
      connectionTracker.updateConnectionStatus(sessionId, 'disconnected');

      // 发射连接状态变化事件
      window.dispatchEvent(new CustomEvent('websocket-connection-change', {
        detail: { sessionId, connected: false }
      }));

      // 尝试重连（如果不是主动关闭）
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.attemptReconnect(sessionId, config);
      }
    };

    this.ws.onerror = (error) => {
      console.error(`❌ UnifiedWebSocketManager: Connection error for ${sessionId}:`, error);
      this.connectionState = 'error';
      connectionTracker.updateConnectionStatus(sessionId, 'error');
    };
  }

  /**
   * 等待WebSocket连接建立
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.ws) {
        reject(new Error('WebSocket not initialized'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);

      this.ws.addEventListener('open', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.ws.addEventListener('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * 发送初始化消息
   */
  private sendInitializationMessage(config: InterviewConfig): void {
    const initMessage = {
      type: 'initialize_session',
      sessionId: this.currentSessionId,
      config: {
        interviewType: config.interviewType,
        language: config.interviewLanguage,
        answerStyle: config.answerStyle
      },
      timestamp: Date.now()
    };

    this.sendMessage(initMessage);
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      console.log(`📨 UnifiedWebSocketManager: Message received:`, data);

      // 触发注册的消息处理器
      this.messageHandlers.forEach((handler, type) => {
        if (data.type === type || type === '*') {
          handler(data);
        }
      });

      // 触发全局事件
      window.dispatchEvent(new CustomEvent('websocket-message', {
        detail: { sessionId: this.currentSessionId, data }
      }));

    } catch (error) {
      console.error(`❌ UnifiedWebSocketManager: Failed to parse message:`, error);
    }
  }

  /**
   * 发送消息
   */
  public sendMessage(message: WebSocketMessage): void {
    if (!this.ws || this.connectionState !== 'connected') {
      console.warn(`⚠️ UnifiedWebSocketManager: Cannot send message, not connected`);
      return;
    }

    try {
      this.ws.send(JSON.stringify(message));
      console.log(`📤 UnifiedWebSocketManager: Message sent:`, message);
    } catch (error) {
      console.error(`❌ UnifiedWebSocketManager: Failed to send message:`, error);
    }
  }

  /**
   * 注册消息处理器
   */
  public onMessage(type: string, handler: (data: any) => void): void {
    this.messageHandlers.set(type, handler);
  }

  /**
   * 移除消息处理器
   */
  public offMessage(type: string): void {
    this.messageHandlers.delete(type);
  }

  /**
   * 尝试重连
   */
  private async attemptReconnect(sessionId: string, config: InterviewConfig): Promise<void> {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;

    console.log(`🔄 UnifiedWebSocketManager: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(async () => {
      try {
        await this.createConnection(sessionId, config);
      } catch (error) {
        console.error(`❌ UnifiedWebSocketManager: Reconnect failed:`, error);
      }
    }, delay);
  }

  /**
   * 构建WebSocket URL
   */
  private buildWebSocketUrl(sessionId: string): string {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const baseUrl = getWsBaseUrl();
    const url = `${baseUrl}/api/ws/interview/${sessionId}?token=${encodeURIComponent(token)}`;

    // 为了调试，可以加一句日志
    console.log(`[UnifiedWebSocketManager] Connecting to: ${url}`);

    return url;
  }

  /**
   * 获取连接状态
   */
  public getConnectionState(): string {
    return this.connectionState;
  }

  /**
   * 获取当前会话ID
   */
  public getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * 断开连接
   */
  public async disconnect(): Promise<void> {
    console.log(`🔌 UnifiedWebSocketManager: Disconnecting`);
    await this.forceDisconnect();
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.messageHandlers.clear();
    this.forceDisconnect();
  }
}

// 导出单例实例
export const unifiedWebSocketManager = UnifiedWebSocketManager.getInstance();

// 导出类型供React Context使用
export type { InterviewConfig, WebSocketMessage };
