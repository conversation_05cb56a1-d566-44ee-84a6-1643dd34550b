import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import AlipayService from '../services/alipayService';

const router = express.Router();

/**
 * 创建订单
 */
router.post('/create', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { itemId, amount, paymentMethod, itemDescription } = req.body;

    // 验证必填字段
    if (!itemId || !amount || !paymentMethod) {
      return res.status(400).json({ 
        success: false,
        message: '缺少必要的订单信息' 
      });
    }

    // 验证金额格式
    const orderAmount = parseFloat(amount);
    if (isNaN(orderAmount) || orderAmount <= 0) {
      return res.status(400).json({ 
        success: false,
        message: '订单金额无效' 
      });
    }

    // 验证支付方式
    if (!['ALIPAY', 'WECHATPAY'].includes(paymentMethod)) {
      return res.status(400).json({ 
        success: false,
        message: '不支持的支付方式' 
      });
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        userId: userId,
        itemId: itemId,
        amount: orderAmount,
        paymentMethod: paymentMethod,
        itemDescription: itemDescription || '',
        status: 'PENDING',
      },
    });

    // 如果是支付宝支付，调用API获取支付二维码
    let paymentInfo = null;
    if (paymentMethod === 'ALIPAY') {
      try {
        // 构建回调URL（需要根据实际部署环境调整）
        const baseUrl = process.env.NODE_ENV === 'production'
          ? 'https://mianshijun.xyz'
          : 'http://localhost:3000';

        const notifyUrl = `${baseUrl}/api/payments/alipay-notify`;
        const returnUrl = `${baseUrl}/payment-success?orderId=${order.id}`;

        // 调用支付宝API获取支付二维码（API接口方式）
        const paymentResponse = await AlipayService.createPayment({
          name: itemDescription || '面试君充值',
          money: orderAmount.toString(),
          out_trade_no: order.id,
          notify_url: notifyUrl,
          return_url: returnUrl,
          param: `userId:${userId}`
        });

        console.log('💰 支付宝API响应:', paymentResponse);

        if (paymentResponse.code === 1) {
          // 支付创建成功，返回二维码信息
          paymentInfo = {
            qrCodeUrl: paymentResponse.qrcode,
            qrImageUrl: paymentResponse.img,
            payUrl: paymentResponse.payurl,
            tradeNo: paymentResponse.trade_no,
            type: 'qrcode' // 标识这是二维码方式
          };

          // 更新订单，保存支付宝交易号
          if (paymentResponse.trade_no) {
            await prisma.order.update({
              where: { id: order.id },
              data: { transactionId: paymentResponse.trade_no }
            });
          }
        } else {
          // 支付创建失败，删除已创建的订单
          await prisma.order.delete({ where: { id: order.id } });

          return res.status(400).json({
            success: false,
            message: paymentResponse.msg || '创建支付失败，请稍后再试'
          });
        }

      } catch (error: any) {
        console.error('❌ 调用支付宝API失败:', error);

        // 删除已创建的订单
        await prisma.order.delete({ where: { id: order.id } });

        return res.status(500).json({
          success: false,
          message: error.message || '创建支付失败，请稍后再试'
        });
      }
    }

    return res.status(201).json({
      success: true,
      orderId: order.id,
      message: '订单创建成功',
      order: {
        id: order.id,
        amount: order.amount,
        status: order.status,
        paymentMethod: order.paymentMethod,
        itemDescription: order.itemDescription,
        createdAt: order.createdAt,
      },
      paymentInfo: paymentInfo
    });

  } catch (error: any) {
    console.error('创建订单失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '创建订单失败，请稍后再试' 
    });
  }
});

/**
 * 获取用户订单列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const {
      page = 1,
      pageSize = 10,
      status
    } = req.query;

    const skip = (Number(page) - 1) * Number(pageSize);

    // 构建查询条件
    const where: any = { userId: userId };

    if (status && status !== 'all') {
      where.status = status;
    }

    // 获取总数和数据
    const [total, orders] = await Promise.all([
      prisma.order.count({ where }),
      prisma.order.findMany({
        where,
        skip,
        take: Number(pageSize),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          amount: true,
          status: true,
          paymentMethod: true,
          itemDescription: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    ]);

    return res.status(200).json({
      success: true,
      orders: orders,
      total: total,
      page: Number(page),
      pageSize: Number(pageSize),
      totalPages: Math.ceil(total / Number(pageSize))
    });

  } catch (error: any) {
    console.error('获取订单列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取订单列表失败，请稍后再试'
    });
  }
});

/**
 * 获取单个订单详情
 */
router.get('/:orderId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({ 
        success: false,
        message: '订单ID无效' 
      });
    }

    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: userId, // 确保用户只能查看自己的订单
      },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        itemId: true,
        itemDescription: true,
        transactionId: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!order) {
      return res.status(404).json({ 
        success: false,
        message: '订单不存在' 
      });
    }

    return res.status(200).json({
      success: true,
      order: order
    });

  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取订单详情失败，请稍后再试' 
    });
  }
});

export default router;
