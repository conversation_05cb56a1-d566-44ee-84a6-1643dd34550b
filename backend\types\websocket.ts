// WebSocket相关类型定义
import { WebSocket } from 'ws';

export interface AuthenticatedWebSocket extends WebSocket {
  userId?: string;
  sessionId?: string;
}

export interface WebSocketMessage {
  type: string;
  payload?: any;
  timestamp?: number;
  mode?: 'live' | 'mock'; // 新增：面试模式标识
  sessionId?: string; // 新增：会话ID
}

export interface AudioChunkMessage extends WebSocketMessage {
  type: 'audio_chunk' | 'pcm_audio_chunk' | 'smart_audio_segment';
  payload: string; // base64 encoded audio data
  format?: string;
  sampleRate?: number;
  channels?: number;
  segmentInfo?: AudioSegmentInfo;
  audioHash?: string; // 音频数据哈希，用于重复检测
  timestamp?: number; // 音频时间戳
  offset?: number; // 音频偏移量
}

export interface AudioSegmentInfo {
  id: string;
  duration: number;
  confidence: number;
  segmentType: 'speech' | 'silence' | 'mixed';
  triggerReason: string;
  timestamp: number;
}

export interface TranscriptionMessage extends WebSocketMessage {
  type: 'transcription';
  text: string;
  speaker: 'interviewer' | 'candidate';
  confidence?: number;
  timestamp: number;
  service?: string;
  isPartial?: boolean;
  segmentId?: string;
  processingTime?: number;
}

// 🔥 新增：实时转录消息类型
export interface TranscriptionPartialMessage extends WebSocketMessage {
  type: 'transcription_partial';
  text: string;
  bubbleId: string;
  timestamp: number;
  confidence?: number;
  service?: string;
}

export interface TranscriptionFinalMessage extends WebSocketMessage {
  type: 'transcription_final';
  text: string;
  bubbleId: string;
  timestamp: number;
  confidence?: number;
  service?: string;
}

export interface AISuggestionMessage extends WebSocketMessage {
  type: 'ai_suggestion_chunk' | 'ai_suggestion_end' | 'ai_suggestion_error';
  text?: string;
  message?: string;
  error?: string;
}

// 🔥 新增：AI模拟面试专用消息类型
export interface MockInterviewQuestionMessage extends WebSocketMessage {
  type: 'mock_interview_question';
  questionId: string;
  questionText: string;
  questionType: 'behavioral' | 'technical' | 'situational' | 'company_specific';
  difficulty: 'easy' | 'medium' | 'hard';
  expectedDuration: number; // 期望回答时长（秒）
  context?: string; // 问题上下文
}

export interface MockInterviewAnswerMessage extends WebSocketMessage {
  type: 'mock_interview_answer';
  questionId: string;
  answerText: string;
  answerDuration: number; // 实际回答时长（秒）
  confidence: number; // 回答置信度
}

export interface MockInterviewFeedbackMessage extends WebSocketMessage {
  type: 'mock_interview_feedback';
  questionId: string;
  feedback: {
    score: number; // 0-100分
    strengths: string[]; // 优点
    improvements: string[]; // 改进建议
    keywordsCovered: string[]; // 覆盖的关键词
    missingKeywords: string[]; // 缺失的关键词
    overallAssessment: string; // 总体评价
  };
}

// 🔥 新增：评价和下一个问题的组合消息类型
export interface MockInterviewEvaluationAndQuestionMessage extends WebSocketMessage {
  type: 'mock_interview_evaluation_and_question';
  questionId: string; // 当前问题的ID
  evaluation: {
    score: number; // 0-100分
    strengths: string[]; // 优点
    improvements: string[]; // 改进建议
    keywordsCovered: string[]; // 覆盖的关键词
    missingKeywords: string[]; // 缺失的关键词
    overallAssessment: string; // 总体评价
    detailedAnalysis?: string; // 详细分析
  };
  nextQuestion: {
    questionId: string; // 下一个问题的ID
    questionText: string;
    questionType: 'behavioral' | 'technical' | 'situational' | 'company_specific';
    difficulty: 'easy' | 'medium' | 'hard';
    expectedDuration: number;
    context?: string;
    keywords?: string[];
  };
}

export interface MockInterviewSessionMessage extends WebSocketMessage {
  type: 'mock_interview_session_start' | 'mock_interview_session_end' | 'mock_interview_session_status';
  sessionInfo?: {
    sessionId: string;
    companyName: string;
    positionName: string;
    totalQuestions: number;
    currentQuestionIndex: number;
    estimatedDuration: number; // 预计总时长（分钟）
  };
  status?: 'preparing' | 'in_progress' | 'waiting_for_answer' | 'generating_question' | 'completed';
}

export interface SystemStatusMessage extends WebSocketMessage {
  type: 'system_status';
  ffmpeg: boolean;
  asr_status: {
    iflytek: string;
    alibaba: string;
    baidu: string;
    whisper: string;
  };
  message: string;
}

export interface ClientLogMessage extends WebSocketMessage {
  type: 'client_log';
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
}

export type IncomingMessage =
  | AudioChunkMessage
  | MockInterviewAnswerMessage
  | ClientLogMessage
  | WebSocketMessage;

export type OutgoingMessage =
  | TranscriptionMessage
  | TranscriptionPartialMessage
  | TranscriptionFinalMessage
  | AISuggestionMessage
  | MockInterviewQuestionMessage
  | MockInterviewFeedbackMessage
  | MockInterviewEvaluationAndQuestionMessage
  | MockInterviewSessionMessage
  | SystemStatusMessage
  | WebSocketMessage;

export interface SessionClient {
  ws: AuthenticatedWebSocket;
  userId: string;
  connectedAt: Date;
}

export interface SessionInfo {
  sessionId: string;
  clients: Set<AuthenticatedWebSocket>;
  createdAt: Date;
  lastActivity: Date;
  status: 'active' | 'completed' | 'error';
}
