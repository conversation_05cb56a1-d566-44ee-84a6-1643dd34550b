import express, { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import prisma from '../../lib/prisma';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 管理员请求接口
interface AdminRequest extends Request {
  adminUser?: { userId: string };
}

// JWT 验证中间件 - 管理员权限
const verifyAdminToken = async (req: AdminRequest, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false,
      message: '未授权访问' 
    });
  }

  const token = authHeader.substring(7);
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string };
    
    // 验证用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, role: true }
    });

    if (!user || user.role !== 'ADMIN') {
      return res.status(403).json({ 
        success: false,
        message: '需要管理员权限' 
      });
    }

    req.adminUser = { userId: user.id };
    next();
  } catch (error) {
    return res.status(401).json({ 
      success: false,
      message: '无效的认证令牌' 
    });
  }
};

/**
 * 获取用户列表
 */
router.get('/', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { 
      page = 1, 
      pageSize = 10, 
      search = '', 
      status = 'all',
      level = 'all'
    } = req.query;

    const skip = (Number(page) - 1) * Number(pageSize);
    
    // 构建查询条件
    const where: any = {};
    
    // 搜索条件（邮箱或昵称）
    if (search) {
      where.OR = [
        { email: { contains: String(search), mode: 'insensitive' } },
        { name: { contains: String(search), mode: 'insensitive' } }
      ];
    }
    
    // 状态筛选（基于role字段）
    if (status !== 'all') {
      if (status === 'normal') {
        where.role = 'USER';
      } else if (status === 'admin') {
        where.role = 'ADMIN';
      }
    }

    // 获取用户列表和总数
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: Number(pageSize),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          balance: {
            select: {
              mockInterviewCredits: true,
              formalInterviewCredits: true,
              mianshijunBalance: true
            }
          },
          orders: {
            where: { status: 'COMPLETED' },
            select: { id: true }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    // 转换数据格式
    const transformedUsers = users.map(user => {
      const totalInterviews = (user.balance?.mockInterviewCredits || 0) + (user.balance?.formalInterviewCredits || 0);
      const hasPaidOrders = user.orders.length > 0;
      
      return {
        id: user.id,
        email: user.email,
        name: user.name || '未设置',
        status: user.role === 'ADMIN' ? '管理员' : '正常',
        level: hasPaidOrders ? '付费用户' : '普通用户',
        interviews: totalInterviews,
        balance: user.balance?.mianshijunBalance || 0,
        registerTime: user.createdAt.toISOString().split('T')[0] // 格式化为 YYYY-MM-DD
      };
    });

    return res.json({
      success: true,
      data: {
        users: transformedUsers,
        total,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / Number(pageSize))
      }
    });

  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

/**
 * 获取单个用户详情
 */
router.get('/:id', verifyAdminToken, async (req: AdminRequest, res: Response) => {
  try {
    const { id } = req.params;

    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        balance: true,
        orders: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        usageRecords: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    return res.json({
      success: true,
      data: { user }
    });

  } catch (error: any) {
    console.error('获取用户详情失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取用户详情失败'
    });
  }
});

export default router;
