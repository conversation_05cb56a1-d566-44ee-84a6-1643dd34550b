/**
 * 配置工具类
 * 提供统一的配置获取和环境判断功能
 */

/**
 * 智能获取前端基础URL
 * 用于生成邀请链接、重定向URL等
 */
export function getFrontendBaseUrl(): string {
  // 1. 优先使用显式配置的FRONTEND_URL环境变量
  if (process.env.FRONTEND_URL) {
    return process.env.FRONTEND_URL;
  }

  // 2. 根据NODE_ENV自动判断
  if (process.env.NODE_ENV === 'production') {
    return 'https://mianshijun.xyz';
  }

  // 3. 开发环境默认值
  return 'http://localhost:5173';
}

/**
 * 获取后端基础URL
 */
export function getBackendBaseUrl(): string {
  // 1. 优先使用显式配置的BACKEND_URL环境变量
  if (process.env.BACKEND_URL) {
    return process.env.BACKEND_URL;
  }

  // 2. 根据NODE_ENV自动判断
  if (process.env.NODE_ENV === 'production') {
    return 'https://mianshijun.xyz';
  }

  // 3. 开发环境默认值
  const port = process.env.PORT || 3000;
  return `http://localhost:${port}`;
}

/**
 * 判断是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 判断是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
}

/**
 * 获取WebSocket URL
 */
export function getWebSocketUrl(): string {
  if (isProduction()) {
    return 'wss://mianshijun.xyz';
  }
  
  const port = process.env.PORT || 3000;
  return `ws://localhost:${port}`;
}

/**
 * 生成完整的邀请链接
 * @param code 邀请码
 * @param type 链接类型：'register' | 'login'
 */
export function generateInviteLink(code: string, type: 'register' | 'login' = 'register'): string {
  const baseUrl = getFrontendBaseUrl();
  return `${baseUrl}/${type}?invite=${code}`;
}

/**
 * 配置验证：检查必要的环境变量是否已设置
 */
export function validateConfig(): { isValid: boolean; missingVars: string[] } {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  return {
    isValid: missingVars.length === 0,
    missingVars
  };
}

/**
 * 获取环境配置摘要（用于日志记录）
 */
export function getConfigSummary(): Record<string, any> {
  return {
    nodeEnv: process.env.NODE_ENV || 'development',
    frontendUrl: getFrontendBaseUrl(),
    backendUrl: getBackendBaseUrl(),
    webSocketUrl: getWebSocketUrl(),
    port: process.env.PORT || 3000,
    isProduction: isProduction(),
    isDevelopment: isDevelopment()
  };
}
