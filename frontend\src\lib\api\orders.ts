import { fetchWithAuth } from './apiService';

// 订单状态类型 - 与后端保持一致
export type OrderStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';

// 订单接口 - 与后端返回数据结构保持一致
export interface Order {
  id: string;
  amount: number | string; // Prisma Decimal 类型在 JavaScript 中可能是 number 或 string
  status: OrderStatus;
  paymentMethod: string;
  itemDescription: string;
  createdAt: string;
  updatedAt: string;
}

// 订单列表响应接口 - 与后端保持一致
export interface OrdersResponse {
  success: boolean;
  orders: Order[];
  total?: number; // 添加可选的总数字段，用于分页
}

// 获取订单列表
export const getOrders = async (params?: {
  page?: number;
  pageSize?: number;
  status?: OrderStatus | 'all';
}): Promise<OrdersResponse> => {
  const searchParams = new URLSearchParams();

  if (params?.page) {
    searchParams.append('page', params.page.toString());
  }
  if (params?.pageSize) {
    searchParams.append('pageSize', params.pageSize.toString());
  }
  if (params?.status && params.status !== 'all') {
    searchParams.append('status', params.status);
  }

  const url = `/orders${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

  // fetchWithAuth 直接返回解析后的JSON数据
  return await fetchWithAuth<OrdersResponse>(url, {
    method: 'GET'
  });
};
