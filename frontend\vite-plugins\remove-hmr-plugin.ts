import type { Plugin } from 'vite';

/**
 * 🔥 增强版Vite插件：彻底移除生产环境HMR客户端代码
 *
 * 这个插件确保在生产构建中完全移除所有HMR相关的代码，
 * 包括WebSocket连接、客户端代码、React Fast Refresh和相关的全局变量。
 *
 * 版本：2.0 - 针对Vite 5.x和React 18.x优化
 */
export function removeHmrPlugin(): Plugin {
  return {
    name: 'remove-hmr-plugin-enhanced',
    apply: 'build', // 只在构建时应用
    enforce: 'post', // 🔥 关键：在所有其他插件之后执行

    config(config, { mode }) {
      const isProduction = mode === 'production';

      if (isProduction) {
        console.log('🚫 RemoveHmrPlugin Enhanced: 生产环境已激活，将彻底移除所有HMR相关代码');

        // 确保define配置存在
        config.define = config.define || {};

        // 🔥 更强力的HMR相关全局变量禁用
        Object.assign(config.define, {
          'import.meta.hot': 'undefined',
          '__vite__hmr': 'undefined',
          '__vite__client': 'undefined',
          'import.meta.env.DEV': 'false',
          'import.meta.env.PROD': 'true',
          // 🔥 新增：React Fast Refresh相关变量
          '__REACT_DEVTOOLS_GLOBAL_HOOK__': 'undefined',
          '__react_refresh__': 'undefined',
          '$RefreshReg$': 'undefined',
          '$RefreshSig$': 'undefined',
          // 🔥 新增：Vite客户端相关变量
          '__vite_plugin_react_preamble_installed__': 'false',
        });

        // 确保server配置不会影响构建
        if (config.server) {
          config.server.hmr = false;
        }

        // 🔥 新增：强制禁用optimizeDeps中的开发依赖
        config.optimizeDeps = config.optimizeDeps || {};
        config.optimizeDeps.exclude = config.optimizeDeps.exclude || [];
        if (!config.optimizeDeps.exclude.includes('@vitejs/plugin-react')) {
          config.optimizeDeps.exclude.push('@vitejs/plugin-react');
        }
      }
    },

    generateBundle(options, bundle) {
      // 🔥 增强版：在生成bundle时彻底检查并移除HMR相关代码
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];

        if (chunk.type === 'chunk' && chunk.code) {
          // 🔥 超强力HMR代码检测模式 - 针对顽固情况
          const criticalHmrPatterns = [
            // Vite HMR相关
            /ws:\/\/.*:5173/g,
            /wss:\/\/.*:5173/g,
            /WebSocket.*5173/g,
            /__vite__hmr/g,
            /__vite__client/g,
            /import\.meta\.hot/g,
            /\[vite\]/g,
            /server connection lost/gi,
            /Polling for restart/gi,
            /connecting\.\.\./gi,
            // 🔥 新增：React Fast Refresh相关
            /__react_refresh__/g,
            /\$RefreshReg\$/g,
            /\$RefreshSig\$/g,
            /__REACT_DEVTOOLS_GLOBAL_HOOK__/g,
            /react-refresh/gi,
            /fast.?refresh/gi,
            // 🔥 新增：更多Vite客户端代码
            /@vite\/client/g,
            /vite\/client/g,
            /hmr\.accept/g,
            /hmr\.dispose/g,
            /module\.hot/g,
            /webpackHotUpdate/g,
            // 🔥 超强力：针对顽固的HMR代码
            /localhost:5173/g,
            /127\.0\.0\.1:5173/g,
            /:5173[^0-9]/g,
            /vite.*hmr/gi,
            /hmr.*vite/gi,
            /setupWebSocket/g,
            /client:.*vite/gi,
            /import\s+.*from\s+['"]@?vite\/client['"]/g,
            /import\s+.*from\s+['"]vite\/client['"]/g,
            // 🔥 移除可能的源码引用
            /\/src\/main\.tsx/g,
            /src\/main\.tsx/g,
          ];

          // 第三方库的热更新代码模式（这些通常是无害的）
          const thirdPartyHmrPatterns = [
            /hotBeforeUpdate/g,
            /componentDidUpdate/g,
            /hot.*reload/gi,
          ];

          let hasCriticalHmrCode = false;
          let hasThirdPartyHmrCode = false;
          let originalCode = chunk.code;
          let cleanedCode = chunk.code;

          // 🔥 更严格的关键HMR代码检查和清理
          criticalHmrPatterns.forEach(pattern => {
            const matches = cleanedCode.match(pattern);
            if (matches) {
              hasCriticalHmrCode = true;
              console.warn(`⚠️ RemoveHmrPlugin Enhanced: 在 ${fileName} 中发现关键HMR代码: ${pattern} (${matches.length} 处)`);

              // 立即清理
              cleanedCode = cleanedCode.replace(pattern, '/* HMR_REMOVED */');
            }
          });

          // 检查第三方库的热更新代码（这些通常是无害的）
          thirdPartyHmrPatterns.forEach(pattern => {
            if (pattern.test(cleanedCode)) {
              hasThirdPartyHmrCode = true;
              console.log(`ℹ️ RemoveHmrPlugin Enhanced: 在 ${fileName} 中发现第三方热更新代码: ${pattern} (通常无害)`);
            }
          });

          // 🔥 应用清理后的代码并注入HMR阻断器
          if (cleanedCode !== originalCode) {
            chunk.code = cleanedCode;
            console.log(`🔧 RemoveHmrPlugin Enhanced: 已从 ${fileName} 中移除关键HMR代码`);
          }

          // 🔥 在主入口文件中注入强力HMR阻断器
          if (fileName.includes('index-') && fileName.endsWith('.js')) {
            const hmrBlocker = `
// 🔥 生产环境HMR强力阻断器
(function() {
  if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
    // 阻断所有HMR相关功能
    window.__vite__hmr = undefined;
    window.__vite__client = undefined;
    window.import = undefined;

    // 重写fetch以阻断源码请求
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
      if (typeof url === 'string' && (url.includes('/src/') || url.includes('main.tsx'))) {
        console.warn('🚫 生产环境阻断源码请求:', url);
        return Promise.reject(new Error('Source code access blocked in production'));
      }
      return originalFetch.call(this, url, options);
    };

    // 阻断WebSocket连接
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
      if (url && (url.includes('5173') || url.includes('localhost'))) {
        console.warn('🚫 生产环境阻断HMR连接:', url);
        return {
          close: function() {},
          send: function() {},
          addEventListener: function() {},
          removeEventListener: function() {},
          readyState: 3 // CLOSED
        };
      }
      return new originalWebSocket(url, protocols);
    };
  }
})();
`;
            chunk.code = hmrBlocker + chunk.code;
            console.log(`🛡️ RemoveHmrPlugin Enhanced: 已在 ${fileName} 中注入HMR阻断器`);
          }

          if (hasCriticalHmrCode) {
            console.error(`❌ RemoveHmrPlugin Enhanced: 文件 ${fileName} 包含关键HMR代码，已强制清理！`);
          } else if (hasThirdPartyHmrCode) {
            console.log(`✅ RemoveHmrPlugin Enhanced: ${fileName} 只包含第三方库的热更新代码，这是正常的`);
          } else {
            console.log(`✅ RemoveHmrPlugin Enhanced: ${fileName} 无HMR代码，符合生产环境要求`);
          }
        }
      });
    },
    
    writeBundle() {
      console.log('✅ RemoveHmrPlugin: 生产构建完成，HMR代码检查已完成');
    }
  };
}
