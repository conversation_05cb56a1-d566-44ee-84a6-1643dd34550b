#!/usr/bin/env node

/**
 * 🔥 构建后清理脚本 - 终极HMR代码清理
 * 在构建完成后对所有文件进行最终的HMR代码清理
 */

const fs = require('fs');
const path = require('path');

const DIST_DIR = path.join(__dirname, '../dist');

// 🔥 超强力HMR代码清理模式
const CLEANUP_PATTERNS = [
  // Vite HMR相关
  { pattern: /ws:\/\/.*:5173/g, replacement: '' },
  { pattern: /wss:\/\/.*:5173/g, replacement: '' },
  { pattern: /WebSocket.*5173/g, replacement: '' },
  { pattern: /__vite__hmr/g, replacement: 'undefined' },
  { pattern: /__vite__client/g, replacement: 'undefined' },
  { pattern: /import\.meta\.hot/g, replacement: 'undefined' },
  { pattern: /\[vite\]/g, replacement: '[PROD]' },
  { pattern: /server connection lost/gi, replacement: '' },
  { pattern: /Polling for restart/gi, replacement: '' },
  { pattern: /connecting\.\.\./gi, replacement: '' },
  
  // React Fast Refresh相关
  { pattern: /__react_refresh__/g, replacement: 'undefined' },
  { pattern: /\$RefreshReg\$/g, replacement: 'undefined' },
  { pattern: /\$RefreshSig\$/g, replacement: 'undefined' },
  { pattern: /__REACT_DEVTOOLS_GLOBAL_HOOK__/g, replacement: 'undefined' },
  { pattern: /react-refresh/gi, replacement: '' },
  { pattern: /fast.?refresh/gi, replacement: '' },
  
  // Vite客户端代码
  { pattern: /@vite\/client/g, replacement: '' },
  { pattern: /vite\/client/g, replacement: '' },
  { pattern: /hmr\.accept/g, replacement: '' },
  { pattern: /hmr\.dispose/g, replacement: '' },
  { pattern: /module\.hot/g, replacement: 'undefined' },
  { pattern: /webpackHotUpdate/g, replacement: 'undefined' },
  
  // 🔥 超强力清理
  { pattern: /localhost:5173/g, replacement: '' },
  { pattern: /127\.0\.0\.1:5173/g, replacement: '' },
  { pattern: /:5173[^0-9]/g, replacement: ':443' },
  { pattern: /vite.*hmr/gi, replacement: '' },
  { pattern: /hmr.*vite/gi, replacement: '' },
  { pattern: /setupWebSocket/g, replacement: '' },
  { pattern: /client:.*vite/gi, replacement: '' },
  
  // 🔥 移除导入语句
  { pattern: /import\s+.*from\s+['"]@?vite\/client['"];?\s*/g, replacement: '' },
  { pattern: /import\s+.*from\s+['"]vite\/client['"];?\s*/g, replacement: '' },
  
  // 🔥 移除源码引用
  { pattern: /\/src\/main\.tsx/g, replacement: '' },
  { pattern: /src\/main\.tsx/g, replacement: '' },
  { pattern: /"\/src\/main\.tsx"/g, replacement: '""' },
  { pattern: /'\/src\/main\.tsx'/g, replacement: "''" },

  // 🔥 移除Vite客户端连接代码
  { pattern: /\[vite\]\s*connecting\.\.\./gi, replacement: '' },
  { pattern: /\[vite\]\s*server connection lost/gi, replacement: '' },
  { pattern: /\[vite\]\s*failed to connect/gi, replacement: '' },
];

function cleanupFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let cleanupCount = 0;

    // 应用所有清理模式
    CLEANUP_PATTERNS.forEach(({ pattern, replacement }) => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        cleanupCount += matches.length;
      }
    });

    // 如果有修改，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🔧 清理文件: ${path.relative(DIST_DIR, filePath)} (${cleanupCount} 处修改)`);
      return cleanupCount;
    }

    return 0;
  } catch (error) {
    console.error(`❌ 清理文件失败: ${filePath}`, error.message);
    return 0;
  }
}

function cleanupDirectory(dir) {
  let totalCleanups = 0;
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        totalCleanups += cleanupDirectory(itemPath);
      } else if (stat.isFile()) {
        // 只处理JS、CSS、HTML文件
        if (item.endsWith('.js') || item.endsWith('.css') || item.endsWith('.html')) {
          totalCleanups += cleanupFile(itemPath);
        }
      }
    });
  } catch (error) {
    console.error(`❌ 扫描目录失败: ${dir}`, error.message);
  }
  
  return totalCleanups;
}

console.log('🧹 开始构建后HMR代码清理...');

if (!fs.existsSync(DIST_DIR)) {
  console.error('❌ dist目录不存在，请先执行构建');
  process.exit(1);
}

const totalCleanups = cleanupDirectory(DIST_DIR);

console.log('');
if (totalCleanups > 0) {
  console.log(`🎉 清理完成！共处理 ${totalCleanups} 处HMR代码`);
} else {
  console.log('✅ 未发现需要清理的HMR代码');
}

console.log('🔍 执行最终验证...');

// 最终验证
const { execSync } = require('child_process');
try {
  execSync('node scripts/verify-build.cjs', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });
} catch (error) {
  console.error('❌ 最终验证失败');
  process.exit(1);
}

console.log('🎉 构建后清理完成！');
