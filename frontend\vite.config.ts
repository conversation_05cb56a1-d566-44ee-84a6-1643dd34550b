import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// ===================================================================
// SIMPLIFIED HMR KILLER PLUGIN
// This plugin uses string replacement to remove HMR client code
// ===================================================================
function hmrKiller(): any {
  return {
    name: 'vite-plugin-hmr-killer',
    enforce: 'post',
    generateBundle(_options: any, bundle: any) {
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        if (chunk.type === 'chunk' && chunk.code) {
          let code = chunk.code;

          // Remove Vite client imports
          code = code.replace(/import\s+.*?from\s+['"]@?vite\/client['"];?\s*/g, '');
          code = code.replace(/import\s+.*?from\s+['"]vite\/client['"];?\s*/g, '');

          // Remove HMR function calls
          code = code.replace(/import\.meta\.hot\.[^;]+;?/g, '');
          code = code.replace(/if\s*\(\s*import\.meta\.hot\s*\)\s*\{[^}]*\}/g, '');

          // Remove WebSocket connections to localhost:5173
          code = code.replace(/new\s+WebSocket\s*\([^)]*5173[^)]*\)/g, 'null');
          code = code.replace(/WebSocket\s*\([^)]*localhost[^)]*\)/g, 'null');

          // Remove HMR messages
          code = code.replace(/\[vite\]/g, '[PROD]');
          code = code.replace(/connecting\.\.\./g, '');
          code = code.replace(/server connection lost/g, '');

          // Log if we made changes
          if (code !== chunk.code) {
            console.log(`[HMR Killer] Cleaned HMR code from: ${fileName}`);
          }

          chunk.code = code;
        }
      });
    }
  };
}

export default defineConfig(({ command }) => {
  const isProduction = command === 'build';

  return {
    plugins: [
      react(),
      // Our custom plugin MUST be active during the build
      isProduction && hmrKiller(),
    ],

    // Define guards to prevent HMR variables from being accessed
    define: {
      'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      // This is a powerful way to disable Vite's HMR logic from within
      '__VITE_HMR_RUNTIME__': 'false',
      'import.meta.hot': 'undefined',
    },
    build: {
      // Minify to also help remove dead code
      minify: 'esbuild',
      // Ensure no sourcemaps are generated in prod
      sourcemap: false,
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },

    // Explicitly clear server options that might relate to HMR
    server: {
      hmr: false,
      // 添加这个配置，允许通过指定的主机名访问开发服务器
      allowedHosts: ['mianshijun.xyz'],
      // API代理配置 - 将/api请求转发到后端3000端口
      proxy: {
        '/api': {
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false,
        }
      }
    }
  };
});
