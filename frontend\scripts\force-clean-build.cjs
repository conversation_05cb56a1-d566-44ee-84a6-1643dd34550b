#!/usr/bin/env node

/**
 * 🔥 强制清理构建脚本
 * 彻底清理所有可能的缓存和临时文件，确保生产构建的纯净性
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.join(__dirname, '..');

// 需要清理的目录和文件
const CLEAN_TARGETS = [
  'dist',
  'node_modules/.vite',
  'node_modules/.cache',
  '.vite',
  '.cache',
  'tsconfig.tsbuildinfo',
  '.eslintcache',
  // 可能的临时文件
  'vite.config.ts.timestamp-*',
  'vite.config.js.timestamp-*',
];

// 需要清理的文件模式
const CLEAN_PATTERNS = [
  /vite\.config\..*\.timestamp-.*/,
  /\.vite\..*\.tmp/,
  /\.cache\..*/,
];

console.log('🧹 开始强制清理构建环境...');

// 清理指定目录和文件
CLEAN_TARGETS.forEach(target => {
  const targetPath = path.join(PROJECT_ROOT, target);
  
  try {
    if (fs.existsSync(targetPath)) {
      const stat = fs.statSync(targetPath);
      
      if (stat.isDirectory()) {
        console.log(`🗂️  删除目录: ${target}`);
        fs.rmSync(targetPath, { recursive: true, force: true });
      } else {
        console.log(`📄 删除文件: ${target}`);
        fs.unlinkSync(targetPath);
      }
    }
  } catch (error) {
    console.warn(`⚠️  清理 ${target} 时出错: ${error.message}`);
  }
});

// 清理匹配模式的文件
function cleanByPatterns(dir) {
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        cleanByPatterns(itemPath);
      } else {
        // 检查是否匹配清理模式
        const shouldClean = CLEAN_PATTERNS.some(pattern => pattern.test(item));
        
        if (shouldClean) {
          console.log(`🎯 删除匹配文件: ${path.relative(PROJECT_ROOT, itemPath)}`);
          fs.unlinkSync(itemPath);
        }
      }
    });
  } catch (error) {
    // 忽略无法访问的目录
  }
}

console.log('🔍 扫描并清理匹配模式的文件...');
cleanByPatterns(PROJECT_ROOT);

// 清理npm缓存（可选）
try {
  console.log('🧽 清理npm缓存...');
  execSync('npm cache clean --force', { 
    cwd: PROJECT_ROOT,
    stdio: 'pipe'
  });
  console.log('✅ npm缓存清理完成');
} catch (error) {
  console.warn('⚠️  npm缓存清理失败，但不影响构建');
}

// 重新安装依赖（确保依赖的纯净性）
try {
  console.log('📦 重新安装依赖...');
  execSync('npm ci', { 
    cwd: PROJECT_ROOT,
    stdio: 'inherit'
  });
  console.log('✅ 依赖重新安装完成');
} catch (error) {
  console.error('❌ 依赖安装失败:', error.message);
  process.exit(1);
}

console.log('');
console.log('🎉 强制清理完成！环境已重置为纯净状态');
console.log('💡 现在可以运行 npm run build 进行纯净构建');
console.log('');
