const fs = require('fs');
const path = require('path');

// 读取当前的vite.config.ts
const configPath = path.join(__dirname, 'vite.config.ts');
let config = fs.readFileSync(configPath, 'utf8');

// 添加更激进的HMR禁用配置
const newConfig = config.replace(
  'export default defineConfig(({ mode }) => {',
  `export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  
  // 生产环境完全禁用客户端代码注入
  if (isProduction) {
    process.env.NODE_ENV = 'production';
  }`
).replace(
  'build: {',
  `build: {
      // 生产环境完全禁用客户端代码
      ...(isProduction && {
        rollupOptions: {
          external: ['/@vite/client'],
          output: {
            manualChunks: undefined,
          },
        },
      }),`
).replace(
  'plugins: [',
  `plugins: isProduction ? [
      react(),
      // 生产环境移除HMR代码的自定义插件
      removeHmrPlugin(),
      // 添加代码替换插件
      {
        name: 'remove-vite-client',
        generateBundle(options, bundle) {
          Object.keys(bundle).forEach(fileName => {
            const chunk = bundle[fileName];
            if (chunk.type === 'chunk' && chunk.code) {
              // 移除所有Vite客户端相关代码
              chunk.code = chunk.code
                .replace(/import\s+[^;]*\/@vite\/client[^;]*;?/g, '')
                .replace(/\/@vite\/client/g, '')
                .replace(/ws:\/\/[^"']*:5173[^"']*/g, '')
                .replace(/wss:\/\/[^"']*:5173[^"']*/g, '')
                .replace(/localhost:5173/g, '')
                .replace(/\[vite\]/g, '')
                .replace(/vite.*connect/gi, '')
                .replace(/server connection lost/gi, '')
                .replace(/polling for restart/gi, '');
            }
          });
        }
      }
    ] : [`
);

fs.writeFileSync(configPath, newConfig);
console.log('✅ Vite配置已更新');
