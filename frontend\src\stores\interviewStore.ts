import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

export type ScreenShareStatus = 'idle' | 'pending' | 'sharing' | 'denied';

// 原有的配置接口
export interface InterviewConfig {
  selectedPositionId: string | null;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  secondaryScreen: boolean;
  audioCollection: boolean;
  screenShareStatus: ScreenShareStatus;
  sharedStream: MediaStream | null;
}

// 新增：面试消息接口
export interface InterviewMessage {
  id: string;
  sender: 'ai' | 'user';
  text: string;
  timestamp: number;
  questionId?: string;
}

// 新增：完整的面试配置接口
export interface FullInterviewConfig {
  companyName: string;
  positionName: string;
  jobDescription: string;
  mode: 'mock' | 'formal';
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
}

interface InterviewState {
  // 原有配置状态
  config: InterviewConfig;

  // 新增：WebSocket通信状态
  sessionId: string | null;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  messages: InterviewMessage[];
  currentQuestionId: string | null;
  fullConfig: FullInterviewConfig | null;
  ws: WebSocket | null;

  // 原有配置方法
  setSelectedPosition: (positionId: string | null) => void;
  setInterviewLanguage: (language: 'chinese' | 'english') => void;
  setAnswerStyle: (style: 'keywords_conversational' | 'conversational') => void;
  setSecondaryScreen: (enabled: boolean) => void;
  setAudioCollection: (enabled: boolean) => void;
  setScreenShareStatus: (status: ScreenShareStatus) => void;
  setSharedStream: (stream: MediaStream | null) => void;
  resetConfig: () => void;

  // 新增：面试通信方法
  startInterview: (config: FullInterviewConfig) => Promise<void>;
  sendAnswer: (answer: string) => void;
  disconnect: () => void;
  clearMessages: () => void;
}

const defaultConfig: InterviewConfig = {
  selectedPositionId: null,
  interviewLanguage: 'chinese',
  answerStyle: 'keywords_conversational',
  secondaryScreen: false,
  audioCollection: false,
  screenShareStatus: 'idle',
  sharedStream: null,
};

const useInterviewStore = create<InterviewState>()(
  immer((set, get) => ({
    // 原有配置状态
    config: defaultConfig,

    // 新增：WebSocket通信状态
    sessionId: null,
    connectionStatus: 'disconnected',
    messages: [],
    currentQuestionId: null,
    fullConfig: null,
    ws: null,
    // 原有配置方法
    setSelectedPosition: (positionId) =>
      set((state) => {
        state.config.selectedPositionId = positionId;
      }),
    setInterviewLanguage: (language) =>
      set((state) => {
        state.config.interviewLanguage = language;
      }),
    setAnswerStyle: (style) =>
      set((state) => {
        state.config.answerStyle = style;
      }),
    setSecondaryScreen: (enabled) =>
      set((state) => {
        state.config.secondaryScreen = enabled;
      }),
    setAudioCollection: (enabled) =>
      set((state) => {
        state.config.audioCollection = enabled;
      }),
    setScreenShareStatus: (status) =>
      set((state) => {
        state.config.screenShareStatus = status;
      }),
    setSharedStream: (stream) => {
      const oldStream = get().config.sharedStream;
      if (oldStream && oldStream !== stream) {
        oldStream.getTracks().forEach(track => track.stop());
        console.log('Old shared stream stopped in store setter.');
      }
      set((state) => {
        state.config.sharedStream = stream;
        state.config.audioCollection = !!(stream && stream.getAudioTracks().length > 0);
      });
    },
    resetConfig: () => {
      const currentStream = get().config.sharedStream;
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        console.log('Shared stream stopped on resetConfig.');
      }
      set((state) => {
        state.config = { ...defaultConfig, sharedStream: null };
      });
    },

    // 新增：面试通信方法
    startInterview: async (config: FullInterviewConfig) => {
      console.log('[InterviewStore] Starting interview with config:', config);

      // 防止重复连接
      const currentStatus = get().connectionStatus;
      if (currentStatus === 'connected' || currentStatus === 'connecting') {
        console.warn('[InterviewStore] Interview already in progress or connecting.');
        return;
      }

      // 生成会话ID
      const sessionId = `mock-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      set((state) => {
        state.connectionStatus = 'connecting';
        state.messages = []; // 清空旧消息
        state.sessionId = sessionId;
        state.fullConfig = config;
        state.currentQuestionId = null;
      });

      try {
        // 建立WebSocket连接
        const wsUrl = `ws://localhost:3001/ws?sessionId=${sessionId}&mode=${config.mode}&userId=mock-user`;
        console.log('[InterviewStore] Connecting to:', wsUrl);

        const ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('[InterviewStore] WebSocket connected successfully');
          set((state) => {
            state.connectionStatus = 'connected';
            state.ws = ws;
          });

          // 连接成功后，立即发送开始指令
          console.log('[InterviewStore] Sending start_mock_interview message');
          ws.send(JSON.stringify({
            type: 'start_mock_interview',
            sessionId,
            config: {
              companyName: config.companyName,
              positionName: config.positionName,
              interviewLanguage: config.interviewLanguage,
              answerStyle: config.answerStyle
            }
          }));
        };

        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('[InterviewStore] Received message:', data);

            // --- 核心：在这里处理所有来自后端的消息 ---
            if (data.type === 'mock_interview_started') {
              console.log('[InterviewStore] Processing mock_interview_started');
              set((state) => {
                const questionText = data.question || data.questionText || '面试开始，请准备回答问题。';
                state.messages.push({
                  id: `ai-${Date.now()}`,
                  sender: 'ai',
                  text: questionText,
                  timestamp: Date.now(),
                  questionId: data.questionId
                });
                state.currentQuestionId = data.questionId;
              });
            } else if (data.type === 'mock_interview_question') {
              console.log('[InterviewStore] Processing mock_interview_question');
              set((state) => {
                state.messages.push({
                  id: `ai-${Date.now()}`,
                  sender: 'ai',
                  text: data.questionText || data.content,
                  timestamp: Date.now(),
                  questionId: data.questionId
                });
                state.currentQuestionId = data.questionId;
              });
            } else if (data.type === 'mock_interview_evaluation_and_question') {
              console.log('[InterviewStore] Processing evaluation and next question');
              set((state) => {
                const evaluationText = `📊 回答评价

评分：${data.evaluation.score}/100

优点：
${data.evaluation.strengths.map(s => `• ${s}`).join('\n')}

改进建议：
${data.evaluation.improvements.map(i => `• ${i}`).join('\n')}

总体评价：${data.evaluation.overallAssessment}



❓ 下一个问题

${data.nextQuestion.questionText}`;

                state.messages.push({
                  id: `ai-eval-${Date.now()}`,
                  sender: 'ai',
                  text: evaluationText,
                  timestamp: Date.now(),
                  questionId: data.nextQuestion.questionId
                });
                state.currentQuestionId = data.nextQuestion.questionId;
              });
            } else if (data.type === 'error') {
              console.error('[InterviewStore] Received error:', data.payload?.message);
              set((state) => {
                state.messages.push({
                  id: `error-${Date.now()}`,
                  sender: 'ai',
                  text: `❌ 出错了: ${data.payload?.message || '未知错误'}`,
                  timestamp: Date.now()
                });
              });
            }
          } catch (error) {
            console.error('[InterviewStore] Error parsing message:', error);
          }
        };

        ws.onclose = () => {
          console.log('[InterviewStore] WebSocket disconnected');
          set((state) => {
            state.connectionStatus = 'disconnected';
            state.ws = null;
          });
        };

        ws.onerror = (error) => {
          console.error('[InterviewStore] WebSocket error:', error);
          set((state) => {
            state.connectionStatus = 'error';
            state.ws = null;
          });
        };

      } catch (error) {
        console.error('[InterviewStore] Failed to create WebSocket:', error);
        set((state) => {
          state.connectionStatus = 'error';
        });
      }
    },

    sendAnswer: (answer: string) => {
      const { ws, sessionId, currentQuestionId, connectionStatus } = get();

      if (connectionStatus !== 'connected' || !ws || !sessionId) {
        console.warn('[InterviewStore] Cannot send answer: not connected');
        return;
      }

      console.log('[InterviewStore] Sending answer:', answer);

      // 先在UI上显示用户的回答
      set((state) => {
        state.messages.push({
          id: `user-${Date.now()}`,
          sender: 'user',
          text: answer,
          timestamp: Date.now()
        });
      });

      // 通过WebSocket发送给后端
      ws.send(JSON.stringify({
        type: 'mock_interview_answer',
        sessionId,
        questionId: currentQuestionId,
        answerText: answer,
        timestamp: Date.now()
      }));
    },

    disconnect: () => {
      console.log('[InterviewStore] Disconnecting...');
      const { ws } = get();

      if (ws) {
        ws.close();
      }

      set((state) => {
        state.connectionStatus = 'disconnected';
        state.sessionId = null;
        state.ws = null;
        state.currentQuestionId = null;
      });
    },

    clearMessages: () => {
      set((state) => {
        state.messages = [];
      });
    }
  }))
);

export default useInterviewStore;
