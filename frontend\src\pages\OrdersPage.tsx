import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { getOrders, Order as BackendOrder, OrderStatus as BackendOrderStatus } from '../lib/api/orders';
import useDocumentTitle from '../hooks/useDocumentTitle';

// UI层的订单状态类型 - 保持原有UI不变
type UIOrderStatus = 'paid' | 'unpaid' | 'cancelled';

// UI层的订单接口 - 保持原有UI不变
interface UIOrder {
  id: string;
  orderNumber: string;
  createTime: string;
  product: string;
  price: string;
  status: UIOrderStatus;
}

// 状态映射函数：后端状态 -> UI状态
const mapBackendStatusToUI = (backendStatus: BackendOrderStatus): UIOrderStatus => {
  switch (backendStatus) {
    case 'COMPLETED':
      return 'paid';
    case 'PENDING':
      return 'unpaid';
    case 'FAILED':
    case 'CANCELLED':
      return 'cancelled';
    default:
      return 'unpaid';
  }
};

// UI状态 -> 后端状态（用于筛选）
const mapUIStatusToBackend = (uiStatus: UIOrderStatus | 'all'): BackendOrderStatus | 'all' => {
  switch (uiStatus) {
    case 'paid':
      return 'COMPLETED';
    case 'unpaid':
      return 'PENDING';
    case 'cancelled':
      return 'CANCELLED';
    case 'all':
      return 'all';
    default:
      return 'all';
  }
};

// 数据转换函数：后端订单 -> UI订单
const transformBackendOrderToUI = (backendOrder: BackendOrder): UIOrder => {
  // 安全地转换 amount 为数字类型
  const amount = typeof backendOrder.amount === 'number'
    ? backendOrder.amount
    : parseFloat(backendOrder.amount.toString());

  return {
    id: backendOrder.id,
    orderNumber: backendOrder.id.slice(-8).toUpperCase(), // 使用订单ID的后8位作为订单号
    createTime: new Date(backendOrder.createdAt).toLocaleString('zh-CN'),
    product: backendOrder.itemDescription || '充值服务',
    price: `¥${amount.toFixed(2)}`,
    status: mapBackendStatusToUI(backendOrder.status)
  };
};



const OrdersPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('我的订单');

  const [orders, setOrders] = useState<UIOrder[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<UIOrderStatus | 'all'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const itemsPerPage = 10;

  // 加载订单数据
  useEffect(() => {
    const loadOrders = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await getOrders({
          page: currentPage,
          pageSize: itemsPerPage,
          status: mapUIStatusToBackend(statusFilter)
        });

        // 调试：打印后端返回的原始数据
        console.log('后端返回的订单数据:', response.orders);

        // 转换后端数据为UI数据
        const uiOrders = response.orders.map(transformBackendOrderToUI);
        setOrders(uiOrders);
        setTotalCount(response.total || 0);
      } catch (err: any) {
        setError(err.message || '获取订单列表失败');
        console.error('获取订单失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadOrders();
  }, [currentPage, statusFilter]);

  // 获取状态显示文本和样式
  const getStatusDisplay = (status: UIOrderStatus) => {
    switch (status) {
      case 'paid':
        return { text: '已支付', className: 'bg-green-100 text-green-800' };
      case 'unpaid':
        return { text: '待支付', className: 'bg-orange-100 text-orange-800' };
      case 'cancelled':
        return { text: '已取消', className: 'bg-gray-100 text-gray-800' };
      default:
        return { text: '未知', className: 'bg-gray-100 text-gray-800' };
    }
  };

  // 使用服务器端分页，不需要前端过滤和分页
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;

  return (
    <div className="container mx-auto px-6 py-6">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">我的订单</h1>
        <p className="text-gray-600 mt-2">查看您的购买记录和订单状态</p>
      </header>

      {/* 状态筛选 */}
      <div className="mb-6">
        <div className="flex gap-2">
          <button
            onClick={() => setStatusFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              statusFilter === 'all'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            全部
          </button>
          <button
            onClick={() => setStatusFilter('paid')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              statusFilter === 'paid'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            已支付
          </button>
          <button
            onClick={() => setStatusFilter('unpaid')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              statusFilter === 'unpaid'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            待支付
          </button>
          <button
            onClick={() => setStatusFilter('cancelled')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              statusFilter === 'cancelled'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            已取消
          </button>
        </div>
      </div>

      {/* 订单表格 */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">加载中...</span>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">订单号</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">创建时间</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">商品</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">价格</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">订单状态</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {orders.map((order) => {
                    const statusDisplay = getStatusDisplay(order.status);
                    return (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 text-sm text-gray-900">{order.orderNumber}</td>
                        <td className="px-6 py-4 text-sm text-gray-600">{order.createTime}</td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {order.product}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">{order.price}</td>
                        <td className="px-6 py-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusDisplay.className}`}>
                            {statusDisplay.text}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-100 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  显示 {startIndex + 1} 到 {Math.min(startIndex + itemsPerPage, totalCount)} 条，共 {totalCount} 条记录
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>
                  <span className="px-3 py-1 text-sm text-gray-600">
                    {currentPage} / {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* 空状态 */}
      {!isLoading && orders.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">暂无订单记录</div>
          <div className="text-gray-500 text-sm">您还没有任何订单，快去购买礼包吧！</div>
        </div>
      )}
    </div>
  );
};

export default OrdersPage;
