// 扣费资源管理器
import { checkAndDeductCredits } from '../lib/api/credits';
import { useBalance } from '../hooks/useBalance';
import { fetchWithAuth } from '../lib/api/apiService';
import useAuthStore from '../stores/authStore';

export interface CreditReservation {
  id: string;
  type: 'mock' | 'formal';
  amount: number;
  reservedAt: number;
  status: 'reserved' | 'confirmed' | 'cancelled' | 'expired';
  expiresAt: number;
}

export interface CreditsState {
  balance: {
    mockInterviewCredits: number;
    formalInterviewCredits: number;
    mianshijunBalance: number;
  };
  reservations: CreditReservation[];
  lastValidated: number;
  isValidating: boolean;
}

/**
 * 扣费资源管理器
 * 负责余额验证、扣费预留、异步扣费等操作
 */
export class CreditsManager {
  private state: CreditsState;
  private validationCache: Map<string, boolean> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
  private reservationTimeout = 10 * 60 * 1000; // 10分钟预留超时
  private pendingDeduction: CreditReservation | null = null;

  constructor() {
    this.state = {
      balance: {
        mockInterviewCredits: 0,
        formalInterviewCredits: 0,
        mianshijunBalance: 0
      },
      reservations: [],
      lastValidated: 0,
      isValidating: false
    };
  }

  /**
   * 简化验证 - 移除预验证逻辑，改为空操作
   */
  public async preValidate(): Promise<void> {
    console.log('🔧 CreditsManager: Pre-validation disabled (using on-demand validation)');

    // 不进行任何预验证操作，余额验证将在需要时进行
    console.log('✅ CreditsManager: Ready (no pre-validation needed)');
  }

  /**
   * 预留扣费 - 在准备阶段预留所需的扣费额度
   */
  public async reserveCredits(type: 'mock' | 'formal'): Promise<{ success: boolean; reservation?: CreditReservation; message?: string }> {
    console.log(`💰 CreditsManager: Reserving credits for ${type} interview`);

    try {
      // 检查余额是否足够
      const balanceCheck = await this.checkSufficientBalance(type);
      if (!balanceCheck.sufficient) {
        return {
          success: false,
          message: balanceCheck.message
        };
      }
      
      // 创建预留记录
      const reservation: CreditReservation = {
        id: this.generateReservationId(),
        type,
        amount: 1,
        reservedAt: Date.now(),
        status: 'reserved',
        expiresAt: Date.now() + this.reservationTimeout
      };

      this.state.reservations.push(reservation);
      this.pendingDeduction = reservation;

      console.log(`✅ CreditsManager: Credits reserved for ${type}:`, reservation.id);
      return {
        success: true,
        reservation
      };
    } catch (error) {
      console.error(`❌ CreditsManager: Failed to reserve credits for ${type}:`, error);
      return {
        success: false,
        message: '系统繁忙，请稍后再试'
      };
    }
  }

  /**
   * 【完全废弃】前端不再负责任何扣费操作
   * 所有积分检查和扣费都由后端在WebSocket消息处理中完成
   */
  public async performSyncDeduction(type: 'mock' | 'formal'): Promise<void> {
    console.log(`🚫 CreditsManager: Frontend deduction COMPLETELY DISABLED - backend handles everything`);

    // 前端现在是"傻瓜终端"，只负责发送意图，不执行任何金融操作
    // 这确保了数据一致性和操作原子性
    return Promise.resolve();
  }

  /**
   * 【完全废弃】前端不再执行任何异步扣费操作
   */
  public async deductCreditsAsync(type: 'mock' | 'formal'): Promise<void> {
    console.log(`🚫 CreditsManager: Async deduction DISABLED - backend handles all credit operations`);
    return Promise.resolve();
  }

  /**
   * 确认扣费 - 在激活阶段确认扣费已完成
   */
  public async confirmDeduction(): Promise<void> {
    console.log('✅ CreditsManager: Confirming deduction');
    
    if (!this.pendingDeduction) {
      console.warn('⚠️ CreditsManager: No pending deduction to confirm');
      return;
    }
    
    try {
      // 等待异步扣费完成（如果还在进行中）
      let attempts = 0;
      const maxAttempts = 30; // 最多等待3秒
      
      while (this.pendingDeduction.status === 'reserved' && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
      
      if (this.pendingDeduction.status === 'confirmed') {
        console.log('✅ CreditsManager: Deduction confirmed successfully');
      } else {
        console.warn('⚠️ CreditsManager: Deduction not yet confirmed, proceeding anyway');
      }
    } catch (error) {
      console.error('❌ CreditsManager: Failed to confirm deduction:', error);
      throw error;
    }
  }

  /**
   * 检查余额是否足够
   */
  public async checkSufficientBalance(type: 'mock' | 'formal'): Promise<{ sufficient: boolean; message?: string }> {
    try {
      // 强制刷新余额，确保获取最新数据（不使用缓存）
      console.log(`🔍 CreditsManager: Force refreshing balance for ${type} check`);
      await this.refreshBalance();

      const fieldName = type === 'mock' ? 'mockInterviewCredits' : 'formalInterviewCredits';
      const hasEnough = this.state.balance[fieldName] > 0;

      console.log(`🔍 CreditsManager: Balance check result for ${type}:`, {
        fieldName,
        currentCredits: this.state.balance[fieldName],
        hasEnough,
        fullBalance: this.state.balance
      });

      // 🔥 添加详细的调试信息
      if (!hasEnough) {
        console.warn(`❌ CreditsManager: Insufficient credits detected!`, {
          type,
          fieldName,
          currentCredits: this.state.balance[fieldName],
          mockCredits: this.state.balance.mockInterviewCredits,
          formalCredits: this.state.balance.formalInterviewCredits,
          mianshijunBalance: this.state.balance.mianshijunBalance,
          timestamp: new Date().toISOString()
        });
      }

      return {
        sufficient: hasEnough,
        message: hasEnough ? undefined : '您的面试机会已用完，可以考虑充值获取更多次数哦'
      };
    } catch (error) {
      console.error('❌ CreditsManager: Failed to check balance:', error);
      return {
        sufficient: false,
        message: '网络状况不佳，请您再试一次或稍后重试'
      };
    }
  }

  /**
   * 获取当前余额
   */
  public getBalance(): CreditsState['balance'] {
    return { ...this.state.balance };
  }

  /**
   * 清除余额验证缓存
   */
  public clearValidationCache(): void {
    console.log('🧹 CreditsManager: Clearing validation cache');
    this.validationCache.clear();
  }

  /**
   * 获取预留记录
   */
  public getReservations(): CreditReservation[] {
    return [...this.state.reservations];
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    console.log('🧹 CreditsManager: Cleaning up resources');
    
    // 取消所有未确认的预留
    this.state.reservations.forEach(reservation => {
      if (reservation.status === 'reserved') {
        reservation.status = 'cancelled';
      }
    });
    
    this.validationCache.clear();
    this.pendingDeduction = null;
    
    console.log('✅ CreditsManager: Cleanup completed');
  }

  // 私有方法
  private async refreshBalance(): Promise<void> {
    try {
      console.log('🔐 CreditsManager: Refreshing balance using fetchWithAuth');

      // 🔥 使用统一的fetchWithAuth方法，与其他API调用保持一致
      const data = await fetchWithAuth<{success: boolean, credits: any}>('/users/me/credits');
      console.log('✅ CreditsManager: API response data:', data);

      // 处理API响应格式
      if (!data.success || !data.credits) {
        throw new Error('Invalid balance response format');
      }

      const credits = data.credits;

      this.state.balance = {
        mockInterviewCredits: credits.mockInterviewCredits || 0,
        formalInterviewCredits: credits.formalInterviewCredits || 0,
        mianshijunBalance: credits.mianshijunBalance || 0
      };

      console.log('✅ CreditsManager: Balance refreshed:', this.state.balance);
    } catch (error) {
      console.error('❌ CreditsManager: Failed to refresh balance:', error);
      throw new Error('Failed to fetch balance');
    }
  }

  private async validateUserPermissions(): Promise<void> {
    // 这里可以添加用户权限验证逻辑
    // 例如检查用户是否有面试权限、是否被封禁等
    console.log('🔐 CreditsManager: User permissions validated');
  }

  /**
   * 【完全废弃】前端不再执行实际扣费操作
   * 所有扣费逻辑都由后端在事务中原子性完成
   */
  private async performAsyncDeduction(type: 'mock' | 'formal'): Promise<void> {
    console.log(`🚫 CreditsManager: performAsyncDeduction DISABLED - backend handles all operations`);
    return Promise.resolve();
  }

  private generateReservationId(): string {
    return `reservation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private cleanupExpiredReservations(): void {
    const now = Date.now();
    this.state.reservations = this.state.reservations.filter(reservation => {
      if (reservation.status === 'reserved' && reservation.expiresAt < now) {
        reservation.status = 'expired';
        return false;
      }
      return true;
    });
  }
}
