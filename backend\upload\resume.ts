import type { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { validateFile, generateSafeFileName } from '../utils/fileValidation';
import path from 'path';
import fs from 'fs';
import multer from 'multer';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface AuthenticatedRequest extends Request {
  userId?: string;
}

// 中间件：验证JWT Token并提取userId（与其他API保持一致）
async function authenticate(req: AuthenticatedRequest): Promise<string | null> {
  console.log('authenticate: Checking authorization header');
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.log('authenticate: No valid authorization header found');
    return null;
  }
  const token = authHeader.split(' ')[1];
  console.log('authenticate: Token found, length:', token.length, ', preview:', token.substring(0, 20) + '...');

  try {
    console.log('authenticate: Verifying token with JWT_SECRET:', JWT_SECRET.substring(0, 3) + '...');
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    console.log('authenticate: Token verified successfully, decoded payload:', decoded);
    console.log('authenticate: Extracted userId:', decoded.userId);
    return decoded.userId;
  } catch (error) {
    console.error('authenticate: JWT verification failed:', error);
    return null;
  }
}

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadsDir = path.join(process.cwd(), 'uploads', 'resumes');
    // 确保目录存在
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // 生成安全的文件名
    const safeFileName = generateSafeFileName(file.originalname);
    cb(null, safeFileName);
  }
});

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = ['application/pdf', 'application/msword',
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                       'text/plain'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，请上传 PDF、Word 或 TXT 文件'));
  }
};

// 配置multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1 // 只允许一个文件
  },
  fileFilter: fileFilter
});

// 认证中间件
export const authenticateMiddleware = async (req: AuthenticatedRequest, res: Response, next: any) => {
  const userId = await authenticate(req);
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  req.userId = userId;
  next();
};

// 文件上传处理器
export const uploadHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        message: 'No file uploaded',
        error: 'NO_FILE'
      });
    }

    const { originalname, filename, path: filePath, size, mimetype } = req.file;
    const userId = req.userId!;

    console.log('📁 Processing file upload:', {
      originalName: originalname,
      safeName: filename,
      filePath: filePath,
      size: size,
      type: mimetype,
      userId: userId
    });

    // 验证上传的文件
    const validation = await validateFile(filePath, originalname);
    if (!validation.isValid) {
      // 清理上传的文件
      try {
        fs.unlinkSync(filePath);
      } catch (cleanupError) {
        console.error('Failed to cleanup uploaded file:', cleanupError);
      }

      return res.status(400).json({
        message: validation.error || 'File validation failed',
        error: 'VALIDATION_FAILED'
      });
    }

    console.log('✅ File uploaded and validated successfully:', {
      originalName: originalname,
      safeName: filename,
      finalPath: filePath,
      detectedType: validation.detectedType,
      userId: userId
    });

    // 返回成功响应
    return res.status(200).json({
      success: true,
      message: 'File uploaded successfully',
      filePath: filePath,
      safeFileName: filename,
      detectedType: validation.detectedType,
      originalName: originalname,
      fileSize: size
    });

  } catch (error) {
    console.error('File upload error:', error);
    return res.status(500).json({
      message: 'File upload failed',
      error: 'UPLOAD_ERROR'
    });
  }
};

// 导出multer中间件和处理器
export { upload };

// 默认导出（保持兼容性）
export default { upload, authenticateMiddleware, uploadHandler };
